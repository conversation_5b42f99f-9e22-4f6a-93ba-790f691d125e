import os

def convert_hl7_to_markdown_batch():
    """
    Fetches unique HL7 message files from a specified input folder,
    and compiles them into multiple Markdown files, with each file
    containing up to a specified number of messages.
    """
    # --- Configuration ---
    # The input path should point to the folder containing your HL7 files.
    # The output path is where the final Markdown files will be saved.
    INPUT_FOLDER_PATH = "C:\\Users\\<USER>\\OneDrive - Primary Health Care Corporation (PHCC)\\Documents\\Hl7-raw-messages"
    OUTPUT_FOLDER_PATH = "C:\\Users\\<USER>\\OneDrive - Primary Health Care Corporation (PHCC)\\Documents\\Hl7-compiled"
    
    # --- Constants ---
    MESSAGES_PER_FILE = 25
    OUTPUT_FILENAME_BASE = "compiled_hl7_messages_part"

    # --- Initialization ---
    unique_messages_with_source = []
    seen_message_content = set()
    
    print("--- Starting HL7 to Markdown Batch Conversion ---")

    # --- Step 1: Validate Input Path ---
    if not os.path.isdir(INPUT_FOLDER_PATH):
        print(f"Error: The specified input folder does not exist: {INPUT_FOLDER_PATH}")
        print("Please check the 'INPUT_FOLDER_PATH' variable in the script.")
        return

    # --- Step 2: Ensure Output Directory Exists ---
    try:
        os.makedirs(OUTPUT_FOLDER_PATH, exist_ok=True)
        print(f"Output directory is ready at: {OUTPUT_FOLDER_PATH}")
    except OSError as e:
        print(f"Error: Could not create the output directory: {OUTPUT_FOLDER_PATH}")
        print(f"Reason: {e}")
        return

    # --- Step 3: Fetch All Unique HL7 Files ---
    print(f"Scanning for all unique HL7 files in: {INPUT_FOLDER_PATH}")
    
    try:
        files_in_directory = sorted(os.listdir(INPUT_FOLDER_PATH))
    except FileNotFoundError:
        print(f"Error: Input directory not found at {INPUT_FOLDER_PATH}. Please check the path.")
        return

    for filename in files_in_directory:
        file_path = os.path.join(INPUT_FOLDER_PATH, filename)

        if os.path.isfile(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().strip()
                    if not content:
                        print(f"  - Skipping empty file: {filename}")
                        continue

                    # Check for duplicates based on content
                    if content not in seen_message_content:
                        seen_message_content.add(content)
                        unique_messages_with_source.append({'content': content, 'filename': filename})
                        print(f"  - Collected unique message from: {filename}")
                    else:
                        print(f"  - Skipping duplicate message from: {filename}")

            except Exception as e:
                print(f"  - Error reading file {filename}: {e}")

    # --- Step 4: Write Messages to Batched Markdown Files ---
    if not unique_messages_with_source:
        print("\nNo unique HL7 messages were found to compile.")
        print("--- Conversion Finished ---")
        return

    print(f"\nFound {len(unique_messages_with_source)} unique messages. Now creating batched Markdown files...")

    total_files_created = 0
    for i in range(0, len(unique_messages_with_source), MESSAGES_PER_FILE):
        # Get a batch of messages
        batch = unique_messages_with_source[i:i + MESSAGES_PER_FILE]
        file_number = (i // MESSAGES_PER_FILE) + 1
        
        markdown_parts = []
        for msg_index, msg_data in enumerate(batch):
            # The message number within the file
            message_number_in_file = msg_index + 1
            
            markdown_parts.append(f"## Message {message_number_in_file}\n")
            markdown_parts.append(f"**Source File:** `{msg_data['filename']}`\n")
            markdown_parts.append("```hl7\n")
            markdown_parts.append(msg_data['content'])
            markdown_parts.append("\n```\n\n---\n")

        # --- Save the current batch to a new MD file ---
        output_filename = f"{OUTPUT_FILENAME_BASE}_{file_number}.md"
        output_filepath = os.path.join(OUTPUT_FOLDER_PATH, output_filename)
        
        final_markdown_content = f"# Compiled HL7 Messages (Part {file_number})\n\n" + "".join(markdown_parts)

        try:
            with open(output_filepath, 'w', encoding='utf-8') as f:
                f.write(final_markdown_content)
            print(f"  - Successfully created: {output_filename} with {len(batch)} messages.")
            total_files_created += 1
        except Exception as e:
            print(f"\nError: Failed to write the output file {output_filename}.")
            print(f"Reason: {e}")
            
    print(f"\n--- Conversion Finished: {total_files_created} file(s) created. ---")


# To run the script, simply call the main function.
if __name__ == "__main__":
    convert_hl7_to_markdown_batch()
