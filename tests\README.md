# HL7 Enhancement Python Tool - Test Suite

This directory contains comprehensive test cases for the HL7 Enhancement Python Tool, ensuring that all components work correctly and that HL7 messages are properly validated against HL7 v2.8 standards.

## 📋 Test Overview

The test suite includes:

1. **HL7 Message Validation Tests** - Validates files in the `enhancedhl7` folder against HL7 v2.8 standards
2. **Unit Tests for Core Scripts** - Tests all main Python modules
3. **Integration Tests** - Tests end-to-end workflows
4. **Error Handling Tests** - Ensures robust error handling

## 🧪 Test Files

### Core Test Modules

- **`test_hl7_validation.py`** - HL7 v2.8 compliance validation tests
- **`test_process_hl7.py`** - Unit tests for the main HL7 processing engine
- **`test_validator.py`** - Unit tests for the HL7 validation tool
- **`test_hl7tomd.py`** - Unit tests for the HL7 to Markdown converter
- **`test_hl7_compliance_fixer.py`** - Unit tests for the compliance fixing tool

### Test Infrastructure

- **`test_runner.py`** - Comprehensive test runner with detailed reporting
- **`test_config.py`** - Test configuration and setup utilities
- **`__init__.py`** - Test package initialization

## 🚀 Running Tests

### Run All Tests

```bash
# Run comprehensive test suite
python tests/test_runner.py
```

### Run Individual Test Modules

```bash
# Run HL7 validation tests
python -m unittest tests.test_hl7_validation

# Run processor tests
python -m unittest tests.test_process_hl7

# Run validator tests
python -m unittest tests.test_validator

# Run converter tests
python -m unittest tests.test_hl7tomd

# Run compliance fixer tests
python -m unittest tests.test_hl7_compliance_fixer
```

### Run Specific Test Classes

```bash
# Run specific test class
python -m unittest tests.test_hl7_validation.TestHL7MessageValidation

# Run specific test method
python -m unittest tests.test_hl7_validation.TestHL7MessageValidation.test_hl7_version_compliance
```

### Verbose Output

```bash
# Run with verbose output
python -m unittest tests.test_hl7_validation -v
```

## 📊 Test Categories

### 1. HL7 Message Validation Tests

**Purpose**: Ensure files in the `enhancedhl7` folder are valid HL7 v2.8 messages

**Test Cases**:
- ✅ HL7 files exist in enhanced folder
- ✅ All messages are HL7 v2.8 compliant
- ✅ MSH segment structure validation
- ✅ Encoding characters validation
- ✅ Message type-specific segment requirements
- ✅ Processing ID compliance

**Requirements**:
- `enhancedHl7` folder must exist with HL7 files
- Files must be valid HL7 format

### 2. HL7 Processor Tests

**Purpose**: Test the main HL7 processing engine (`processHl7.py`)

**Test Cases**:
- ✅ Processor initialization
- ✅ Directory creation and management
- ✅ HL7 file discovery
- ✅ Message parsing (valid and invalid)
- ✅ HL7 enhancements application
- ✅ EVN segment handling for ADT messages
- ✅ Configuration loading
- ✅ File saving and quarantine handling
- ✅ Error handling and statistics

### 3. Validator Tests

**Purpose**: Test the HL7 validation tool (`validator.py`)

**Test Cases**:
- ✅ HL7 file parsing
- ✅ Version validation
- ✅ MSH segment validation
- ✅ Required segments validation
- ✅ Message type extraction
- ✅ Single file validation
- ✅ Folder validation
- ✅ Report generation

### 4. HL7 to Markdown Converter Tests

**Purpose**: Test the HL7 to Markdown converter (`hl7tomd.py`)

**Test Cases**:
- ✅ Batch conversion process
- ✅ Duplicate message removal
- ✅ Markdown formatting
- ✅ Message batching
- ✅ File encoding handling
- ✅ Error handling for invalid files
- ✅ Progress reporting

### 5. Compliance Fixer Tests

**Purpose**: Test the HL7 compliance fixing tool (`hl7_compliance_fixer.py`)

**Test Cases**:
- ✅ Fixer initialization
- ✅ MSH segment detection
- ✅ EVN segment creation
- ✅ ADT message enhancement
- ✅ File backup functionality
- ✅ Batch processing
- ✅ Error handling and statistics

## 🔧 Test Configuration

### Environment Setup

The test suite automatically creates temporary test environments with:
- Sample HL7 files (valid and invalid)
- Directory structures
- Configuration files
- Mock data

### Sample Test Data

The test suite includes various HL7 message types:
- **ADT^A04** - Patient Registration
- **ADT^A08** - Patient Update
- **ORU^R01** - Lab Results
- **SIU^S12** - Appointment Scheduling

### Test Configuration Options

```python
# In test_config.py
class TestConfig:
    ENHANCED_HL7_DIR = "enhancedHl7"
    RAW_HL7_DIR = "rawhl7messages"
    QUARANTINE_DIR = "quarantine"
    # ... more configuration options
```

## 📈 Test Reports

### Comprehensive Test Report

The test runner generates detailed reports including:
- Overall test statistics
- Per-module results
- Failure and error details
- Performance metrics
- Recommendations

### Report Files

- **Console Output** - Real-time test results
- **JSON Report** - Detailed test report saved to `test_report_YYYYMMDD_HHMMSS.json`

### Sample Report Output

```
================================================================================
HL7 ENHANCEMENT PYTHON TOOL - COMPREHENSIVE TEST SUITE
================================================================================
Starting test execution at: 2024-11-10 15:30:00

Running HL7 Message Validation tests...
------------------------------------------------------------
Results for HL7 Message Validation:
  Total Tests: 6
  ✅ Passed: 6
  ❌ Failed: 0
  🚨 Errors: 0
  ⏭️  Skipped: 0
  📊 Success Rate: 100.0%

...

================================================================================
COMPREHENSIVE TEST REPORT
================================================================================
Execution Time: 12.34 seconds
Total Test Suites: 6
Total Tests: 45
✅ Passed: 43
❌ Failed: 1
🚨 Errors: 1
⏭️  Skipped: 0
📊 Overall Success Rate: 95.6%
```

## 🛠️ Prerequisites

### Required Dependencies

```bash
pip install -r requirements.txt
```

### Required Packages

- `unittest` (built-in)
- `hl7` - HL7 message parsing
- `pathlib` (built-in)
- `json` (built-in)
- `tempfile` (built-in)

### Test Data Requirements

For HL7 validation tests to run properly:
1. Create `enhancedHl7` folder in project root
2. Add sample HL7 files to the folder
3. Ensure files are HL7 v2.8 compliant

## 🚨 Troubleshooting

### Common Issues

1. **"No HL7 files found"**
   - Ensure `enhancedHl7` folder exists
   - Add `.hl7` files to the folder

2. **Import errors**
   - Ensure you're running from project root
   - Check Python path configuration

3. **Permission errors**
   - Ensure write permissions for test directories
   - Check file system permissions

### Debug Mode

```bash
# Run with maximum verbosity
python -m unittest tests.test_hl7_validation -v

# Run single test for debugging
python -m unittest tests.test_hl7_validation.TestHL7MessageValidation.test_hl7_version_compliance -v
```

## 🤝 Contributing

When adding new tests:

1. Follow existing naming conventions
2. Include docstrings for test methods
3. Use appropriate assertions
4. Handle cleanup in `tearDown` methods
5. Add test cases to the comprehensive runner

### Test Naming Convention

```python
def test_feature_scenario(self):
    """Test description of what is being tested"""
    # Test implementation
```

## 📝 Test Coverage

The test suite aims for comprehensive coverage of:
- ✅ All public methods and functions
- ✅ Error handling paths
- ✅ Edge cases and boundary conditions
- ✅ Integration scenarios
- ✅ Configuration variations

## 🎯 Continuous Integration

These tests are designed to be run in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run HL7 Tests
  run: python tests/test_runner.py
```

The test runner exits with appropriate codes:
- `0` - All tests passed
- `1` - Some tests failed

---

For more information about the HL7 Enhancement Python Tool, see the main [README.md](../README.md) file.
