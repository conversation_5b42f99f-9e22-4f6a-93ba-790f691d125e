"""
Test configuration and setup utilities for HL7 Enhancement Python Tool tests
"""

import os
import tempfile
import shutil
from pathlib import Path
import json


class TestConfig:
    """Configuration class for test settings"""
    
    # Test directories
    TEST_DATA_DIR = "test_data"
    ENHANCED_HL7_DIR = "enhancedHl7"
    RAW_HL7_DIR = "rawhl7messages"
    QUARANTINE_DIR = "quarantine"
    
    # Sample HL7 messages for testing
    SAMPLE_ADT_A04 = (
        "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\r"
        "EVN|A04|202411101127|||123456\r"
        "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F|||123 Main St^^Hometown^CA^12345^USA||(123)456-7890|||S|C|123456789\r"
        "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr.|||||||||||1234567890"
    )
    
    SAMPLE_ADT_A08 = (
        "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101128||ADT^A08|123457|P|2.8\r"
        "EVN|A08|202411101128|||123457\r"
        "PID|1||123457^^^Hospital^MR||Smith^John^B^^Mr.||19750615|M|||456 Oak Ave^^Cityville^NY^54321^USA||(555)123-4567|||M|C|987654321\r"
        "PV1|1|O|ER^02^01||||67890^Johnson^Mary^C^^Dr.|||||||||||9876543210"
    )
    
    SAMPLE_ORU_R01 = (
        "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101129||ORU^R01|123458|P|2.8\r"
        "PID|1||123458^^^Hospital^MR||Brown^Alice^C^^Mrs.||19820315|F|||789 Pine St^^Townsville^TX^67890^USA||(444)555-6666|||S|C|456789123\r"
        "OBR|1|123458|789012|CBC^Complete Blood Count||202411101129|||||||||||67890^Johnson^Mary^C^^Dr.\r"
        "OBX|1|NM|WBC^White Blood Cell Count|1|7.5|10*3/uL|4.0-11.0|N|||F"
    )
    
    SAMPLE_SIU_S12 = (
        "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101130||SIU^S12|123459|P|2.8\r"
        "SCH|123459|||||ROUTINE^Routine Appointment|SCHEDULED^Scheduled|30|MIN|202411151400|202411151430\r"
        "PID|1||123459^^^Hospital^MR||Wilson^Robert^D^^Mr.||19700920|M|||321 Elm St^^Villagetown^FL^13579^USA||(777)888-9999|||M|C|789123456"
    )
    
    # Invalid HL7 samples for testing error handling
    INVALID_HL7_NO_MSH = (
        "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F\r"
        "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr."
    )
    
    INVALID_HL7_WRONG_VERSION = (
        "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.5\r"
        "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F"
    )
    
    INVALID_HL7_MISSING_SEGMENTS = (
        "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\r"
        "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F"
    )
    
    # Sample mapping rules for testing
    SAMPLE_MAPPING_RULES = {
        "QATAR_ID_EXP": {
            "target_segment": "PID",
            "target_field": "3.8",
            "description": "Qatar ID Expiration Date"
        },
        "HC_EXP_DATE": {
            "target_segment": "PID",
            "target_field": "3.8",
            "description": "Health Card Expiration Date"
        },
        "FAMILY_PHYSICIAN": {
            "target_segment": "ROL",
            "target_field": "4",
            "description": "Family Physician"
        },
        "PRIM_ORG_NAME": {
            "target_segment": "PD1",
            "target_field": "3",
            "description": "Primary Organization Name"
        }
    }


class TestDataSetup:
    """Utility class for setting up test data and environments"""
    
    def __init__(self, base_dir=None):
        self.base_dir = Path(base_dir) if base_dir else Path(tempfile.mkdtemp())
        self.config = TestConfig()
    
    def create_test_environment(self):
        """Create a complete test environment with directories and sample files"""
        # Create directories
        directories = [
            self.config.TEST_DATA_DIR,
            self.config.ENHANCED_HL7_DIR,
            self.config.RAW_HL7_DIR,
            self.config.QUARANTINE_DIR
        ]
        
        for directory in directories:
            dir_path = self.base_dir / directory
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Create sample HL7 files
        self.create_sample_hl7_files()
        
        # Create mapping rules file
        self.create_mapping_rules_file()
        
        return self.base_dir
    
    def create_sample_hl7_files(self):
        """Create sample HL7 files for testing"""
        # Valid HL7 files
        valid_files = [
            ("ADT_A04_sample.hl7", self.config.SAMPLE_ADT_A04),
            ("ADT_A08_sample.hl7", self.config.SAMPLE_ADT_A08),
            ("ORU_R01_sample.hl7", self.config.SAMPLE_ORU_R01),
            ("SIU_S12_sample.hl7", self.config.SAMPLE_SIU_S12)
        ]
        
        # Create valid files in both raw and enhanced directories
        for filename, content in valid_files:
            # Raw HL7 files (for processing)
            raw_file = self.base_dir / self.config.RAW_HL7_DIR / filename
            with open(raw_file, 'w') as f:
                f.write(content)
            
            # Enhanced HL7 files (for validation testing)
            enhanced_file = self.base_dir / self.config.ENHANCED_HL7_DIR / filename
            with open(enhanced_file, 'w') as f:
                f.write(content)
        
        # Invalid HL7 files for error testing
        invalid_files = [
            ("invalid_no_msh.hl7", self.config.INVALID_HL7_NO_MSH),
            ("invalid_wrong_version.hl7", self.config.INVALID_HL7_WRONG_VERSION),
            ("invalid_missing_segments.hl7", self.config.INVALID_HL7_MISSING_SEGMENTS)
        ]
        
        for filename, content in invalid_files:
            test_file = self.base_dir / self.config.TEST_DATA_DIR / filename
            with open(test_file, 'w') as f:
                f.write(content)
    
    def create_mapping_rules_file(self):
        """Create mapping rules JSON file"""
        mapping_file = self.base_dir / "mapping_rules.json"
        with open(mapping_file, 'w') as f:
            json.dump(self.config.SAMPLE_MAPPING_RULES, f, indent=2)
    
    def create_subdirectory_structure(self):
        """Create subdirectory structure with HL7 files for testing recursive processing"""
        # Create subdirectories
        subdirs = [
            "lab_results",
            "admissions",
            "appointments",
            "lab_results/chemistry",
            "lab_results/hematology"
        ]
        
        for subdir in subdirs:
            subdir_path = self.base_dir / self.config.RAW_HL7_DIR / subdir
            subdir_path.mkdir(parents=True, exist_ok=True)
            
            # Add sample files to each subdirectory
            if "lab_results" in subdir:
                sample_file = subdir_path / "lab_sample.hl7"
                with open(sample_file, 'w') as f:
                    f.write(self.config.SAMPLE_ORU_R01)
            elif "admissions" in subdir:
                sample_file = subdir_path / "admission_sample.hl7"
                with open(sample_file, 'w') as f:
                    f.write(self.config.SAMPLE_ADT_A04)
            elif "appointments" in subdir:
                sample_file = subdir_path / "appointment_sample.hl7"
                with open(sample_file, 'w') as f:
                    f.write(self.config.SAMPLE_SIU_S12)
    
    def cleanup(self):
        """Clean up test environment"""
        if self.base_dir.exists():
            shutil.rmtree(self.base_dir)


class TestUtilities:
    """Utility functions for testing"""
    
    @staticmethod
    def create_temporary_hl7_file(content, filename="temp.hl7"):
        """Create a temporary HL7 file with given content"""
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, filename)
        
        with open(file_path, 'w') as f:
            f.write(content)
        
        return file_path, temp_dir
    
    @staticmethod
    def compare_hl7_messages(message1, message2, ignore_timestamps=True):
        """Compare two HL7 messages for equality, optionally ignoring timestamps"""
        if ignore_timestamps:
            # Remove timestamp fields for comparison
            msg1_lines = message1.split('\r')
            msg2_lines = message2.split('\r')
            
            # Compare line by line, ignoring MSH-7 (timestamp)
            if len(msg1_lines) != len(msg2_lines):
                return False
            
            for line1, line2 in zip(msg1_lines, msg2_lines):
                if line1.startswith('MSH') and line2.startswith('MSH'):
                    # Compare MSH segments without timestamp
                    fields1 = line1.split('|')
                    fields2 = line2.split('|')
                    
                    if len(fields1) != len(fields2):
                        return False
                    
                    for i, (field1, field2) in enumerate(zip(fields1, fields2)):
                        if i != 7 and field1 != field2:  # Skip timestamp field (index 7)
                            return False
                else:
                    if line1 != line2:
                        return False
            
            return True
        else:
            return message1 == message2
    
    @staticmethod
    def extract_message_type(hl7_content):
        """Extract message type from HL7 content"""
        lines = hl7_content.split('\r')
        for line in lines:
            if line.startswith('MSH'):
                fields = line.split('|')
                if len(fields) > 9:
                    message_type_field = fields[9]
                    if '^' in message_type_field:
                        return message_type_field.split('^')[0]
                    return message_type_field
        return None
    
    @staticmethod
    def validate_hl7_structure(hl7_content):
        """Basic validation of HL7 message structure"""
        if not hl7_content.strip():
            return False, "Empty content"
        
        if not hl7_content.startswith('MSH'):
            return False, "Does not start with MSH segment"
        
        # Check for proper field separators
        if '|' not in hl7_content:
            return False, "Missing field separators"
        
        return True, "Valid structure"


# Global test configuration instance
TEST_CONFIG = TestConfig()

# Convenience function for creating test environments
def create_test_environment(base_dir=None):
    """Create a test environment and return the setup object"""
    setup = TestDataSetup(base_dir)
    setup.create_test_environment()
    return setup
