#!/usr/bin/env python3
"""
HL7 v2.8 Compliance Fixer
Automatically fixes HL7 messages to ensure they comply with HL7 v2.8 standards.
"""

import os
import re
import logging
import hl7
from datetime import datetime
from typing import List, Tuple, Optional
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hl7_compliance_fixer.log'),
        logging.StreamHandler()
    ]
)

class HL7ComplianceFixer:
    def __init__(self, folder_path: str, backup_folder: str = "backup_before_fix"):
        self.folder_path = folder_path
        self.backup_folder = backup_folder
        self.logger = logging.getLogger(__name__)
        self.fixed_count = 0
        self.error_count = 0
        self.already_compliant_count = 0
        
        # Create backup folder if it doesn't exist
        if not os.path.exists(self.backup_folder):
            os.makedirs(self.backup_folder)
            self.logger.info(f"Created backup folder: {self.backup_folder}")

    def create_evn_segment(self, message_type: str, timestamp: str = None) -> str:
        """Create a proper EVN segment for ADT messages"""
        if not timestamp:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        
        # EVN segment format: EVN|Event Type Code|Recorded Date/Time|Date/Time Planned Event|Event Reason Code|Operator ID|Event Occurred
        # For ADT^A04 (Register a patient), the event type code is A04
        event_code = message_type.split('^')[1] if '^' in message_type else 'A04'
        evn_segment = f"EVN|{event_code}|{timestamp}||||"
        
        return evn_segment

    def fix_missing_evn_segment(self, content: str) -> Tuple[str, bool]:
        """Add missing EVN segment after MSH for ADT messages"""
        lines = content.strip().split('\n')
        if not lines:
            return content, False

        # Check if EVN segment already exists
        has_evn = any(line.strip().startswith('EVN|') for line in lines if line.strip())
        if has_evn:
            return content, False  # No fix needed

        # Find MSH segment and extract message type
        msh_line = None
        message_type = None
        msh_index = -1
        for i, line in enumerate(lines):
            if line.strip().startswith('MSH|'):
                msh_line = line.strip()
                msh_index = i
                # Extract message type from MSH-9 field
                fields = line.split('|')
                if len(fields) > 9:
                    message_type = fields[9].strip()
                break

        if not msh_line or not message_type or msh_index == -1:
            return content, False

        # Only add EVN for ADT messages
        if not message_type.startswith('ADT^'):
            return content, False

        # Extract timestamp from MSH-7 field
        msh_fields = msh_line.split('|')
        timestamp = msh_fields[7].strip() if len(msh_fields) > 7 else None

        # Create EVN segment
        evn_segment = self.create_evn_segment(message_type, timestamp)

        # Insert EVN segment after MSH
        lines.insert(msh_index + 1, evn_segment)

        return '\n'.join(lines), True

    def fix_hl7_version(self, content: str) -> Tuple[str, bool]:
        """Ensure HL7 version is set to 2.8"""
        lines = content.strip().split('\n')
        fixed = False
        
        for i, line in enumerate(lines):
            if line.startswith('MSH|'):
                fields = line.split('|')
                if len(fields) > 12:
                    current_version = fields[12].strip()
                    if current_version != '2.8':
                        fields[12] = '2.8'
                        lines[i] = '|'.join(fields)
                        fixed = True
                        self.logger.info(f"Fixed HL7 version from '{current_version}' to '2.8'")
                break
        
        return '\n'.join(lines), fixed

    def fix_message_structure(self, content: str) -> Tuple[str, bool]:
        """Fix basic message structure issues"""
        # Ensure proper line endings
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # Remove empty lines at the end
        content = content.rstrip() + '\n'
        
        return content, True

    def fix_hl7_message(self, file_path: str) -> bool:
        """Fix a single HL7 message file"""
        try:
            # Read original content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Create backup
            backup_path = os.path.join(self.backup_folder, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            
            content = original_content
            any_fixes = False
            fixes_applied = []
            
            # Apply fixes
            content, fixed = self.fix_hl7_version(content)
            if fixed:
                any_fixes = True
                fixes_applied.append("HL7 version")
            
            content, fixed = self.fix_missing_evn_segment(content)
            if fixed:
                any_fixes = True
                fixes_applied.append("Missing EVN segment")
            
            content, fixed = self.fix_message_structure(content)
            if fixed and content != original_content:
                any_fixes = True
                fixes_applied.append("Message structure")
            
            if any_fixes:
                # Write fixed content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixed_count += 1
                self.logger.info(f"Fixed {file_path}: {', '.join(fixes_applied)}")
                return True
            else:
                self.already_compliant_count += 1
                self.logger.debug(f"No fixes needed for {file_path}")
                return False
                
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error fixing {file_path}: {str(e)}")
            return False

    def fix_all_messages(self) -> None:
        """Fix all HL7 messages in the folder"""
        self.logger.info(f"Starting HL7 compliance fixing for folder: {self.folder_path}")
        
        if not os.path.exists(self.folder_path):
            self.logger.error(f"Folder not found: {self.folder_path}")
            return
        
        # Get all .hl7 files
        hl7_files = [f for f in os.listdir(self.folder_path) if f.endswith('.hl7')]
        
        if not hl7_files:
            self.logger.warning(f"No .hl7 files found in {self.folder_path}")
            return
        
        self.logger.info(f"Found {len(hl7_files)} HL7 files to process")
        
        for file_name in sorted(hl7_files):
            file_path = os.path.join(self.folder_path, file_name)
            self.fix_hl7_message(file_path)
        
        # Print summary
        total_files = len(hl7_files)
        self.logger.info("\n" + "="*80)
        self.logger.info("HL7 COMPLIANCE FIXING SUMMARY")
        self.logger.info("="*80)
        self.logger.info(f"Total files processed: {total_files}")
        self.logger.info(f"Files fixed: {self.fixed_count}")
        self.logger.info(f"Files already compliant: {self.already_compliant_count}")
        self.logger.info(f"Files with errors: {self.error_count}")
        self.logger.info(f"Success rate: {((self.fixed_count + self.already_compliant_count) / total_files * 100):.2f}%")
        self.logger.info(f"Backup folder: {self.backup_folder}")
        self.logger.info("="*80)

def main():
    """Main function"""
    folder_path = "enhancedHl7"
    
    fixer = HL7ComplianceFixer(folder_path)
    fixer.fix_all_messages()

if __name__ == "__main__":
    main()
