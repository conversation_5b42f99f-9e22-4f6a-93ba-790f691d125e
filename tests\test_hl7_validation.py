"""
Test cases for HL7 message validation against HL7 v2.8 standards.
Tests files in the enhancedhl7 folder for compliance with HL7 v2.8 specifications.
"""

import unittest
import os
import hl7
from pathlib import Path
from typing import List, Dict, Tuple
import re
import json
from datetime import datetime


class TestHL7MessageValidation(unittest.TestCase):
    """Test suite for validating HL7 messages against v2.8 standards"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class with configuration"""
        cls.enhanced_hl7_folder = "enhancedHl7"
        cls.target_version = "2.8"
        cls.test_results = []
        
        # HL7 v2.8 Required segments for message types
        cls.required_segments = {
            'ACK': ['MSH', 'MSA'],
            'ADT': ['MSH', 'EVN', 'PID'],
            'ORM': ['MSH', 'PID', 'ORC'],
            'ORU': ['MSH', 'PID', 'OBR'],
            'SIU': ['MSH', 'SCH'],
            'MDM': ['MSH', 'EVN', 'PID', 'TXA'],
            'DFT': ['MSH', 'EVN', 'PID', 'FT1'],
            'BAR': ['MSH', 'EVN', 'PID'],
            'VXU': ['MSH', 'PID', 'RXA']
        }
        
        # Valid field separators for HL7 v2.8
        cls.valid_field_separator = '|'
        cls.valid_component_separator = '^'
        cls.valid_repetition_separator = '~'
        cls.valid_escape_character = '\\'
        cls.valid_subcomponent_separator = '&'
    
    def setUp(self):
        """Set up each test"""
        self.hl7_files = self._discover_hl7_files()
    
    def _discover_hl7_files(self) -> List[Path]:
        """Discover all HL7 files in the enhanced folder"""
        hl7_files = []
        if os.path.exists(self.enhanced_hl7_folder):
            for root, dirs, files in os.walk(self.enhanced_hl7_folder):
                for file in files:
                    if file.endswith('.hl7'):
                        hl7_files.append(Path(root) / file)
        return hl7_files
    
    def _parse_hl7_file(self, file_path: Path) -> Tuple[hl7.Message, str]:
        """Parse HL7 file and return message object and raw content"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
            
            # Handle different line endings
            if '\n' in content and '\r' not in content:
                content = content.replace('\n', '\r')
            
            message = hl7.parse(content)
            return message, content
        except Exception as e:
            self.fail(f"Failed to parse HL7 file {file_path}: {str(e)}")
    
    def test_hl7_files_exist(self):
        """Test that HL7 files exist in the enhanced folder"""
        self.assertTrue(
            os.path.exists(self.enhanced_hl7_folder),
            f"Enhanced HL7 folder '{self.enhanced_hl7_folder}' does not exist"
        )
        self.assertGreater(
            len(self.hl7_files),
            0,
            f"No HL7 files found in '{self.enhanced_hl7_folder}'"
        )
    
    def test_hl7_version_compliance(self):
        """Test that all HL7 messages are version 2.8 compliant"""
        if not self.hl7_files:
            self.skipTest("No HL7 files found to test")
        
        failed_files = []
        for file_path in self.hl7_files:
            message, _ = self._parse_hl7_file(file_path)
            
            try:
                msh = message.segment('MSH')
                version = str(msh[12]) if len(msh) > 12 else ""
                
                if version != self.target_version:
                    failed_files.append({
                        'file': str(file_path),
                        'expected': self.target_version,
                        'actual': version
                    })
            except Exception as e:
                failed_files.append({
                    'file': str(file_path),
                    'error': f"Failed to check version: {str(e)}"
                })
        
        self.assertEqual(
            len(failed_files), 0,
            f"Files with incorrect HL7 version: {json.dumps(failed_files, indent=2)}"
        )
    
    def test_msh_segment_structure(self):
        """Test MSH segment structure compliance with HL7 v2.8"""
        if not self.hl7_files:
            self.skipTest("No HL7 files found to test")
        
        failed_files = []
        for file_path in self.hl7_files:
            message, _ = self._parse_hl7_file(file_path)
            
            try:
                msh = message.segment('MSH')
                
                # Test required MSH fields
                required_fields = {
                    1: "Field Separator",
                    2: "Encoding Characters", 
                    3: "Sending Application",
                    4: "Sending Facility",
                    5: "Receiving Application",
                    6: "Receiving Facility",
                    7: "Date/Time of Message",
                    9: "Message Type",
                    10: "Message Control ID",
                    11: "Processing ID",
                    12: "Version ID"
                }
                
                missing_fields = []
                for field_num, field_name in required_fields.items():
                    if len(msh) <= field_num or not str(msh[field_num]).strip():
                        missing_fields.append(f"MSH-{field_num} ({field_name})")
                
                if missing_fields:
                    failed_files.append({
                        'file': str(file_path),
                        'missing_fields': missing_fields
                    })
                    
            except Exception as e:
                failed_files.append({
                    'file': str(file_path),
                    'error': f"Failed to validate MSH segment: {str(e)}"
                })
        
        self.assertEqual(
            len(failed_files), 0,
            f"Files with invalid MSH segments: {json.dumps(failed_files, indent=2)}"
        )
    
    def test_encoding_characters(self):
        """Test that encoding characters are properly set according to HL7 v2.8"""
        if not self.hl7_files:
            self.skipTest("No HL7 files found to test")
        
        failed_files = []
        expected_encoding = "^~\\&"
        
        for file_path in self.hl7_files:
            message, _ = self._parse_hl7_file(file_path)
            
            try:
                msh = message.segment('MSH')
                encoding_chars = str(msh[2]) if len(msh) > 2 else ""
                
                if encoding_chars != expected_encoding:
                    failed_files.append({
                        'file': str(file_path),
                        'expected': expected_encoding,
                        'actual': encoding_chars
                    })
                    
            except Exception as e:
                failed_files.append({
                    'file': str(file_path),
                    'error': f"Failed to check encoding characters: {str(e)}"
                })
        
        self.assertEqual(
            len(failed_files), 0,
            f"Files with incorrect encoding characters: {json.dumps(failed_files, indent=2)}"
        )
    
    def test_message_type_specific_segments(self):
        """Test that messages contain required segments for their message type"""
        if not self.hl7_files:
            self.skipTest("No HL7 files found to test")
        
        failed_files = []
        for file_path in self.hl7_files:
            message, _ = self._parse_hl7_file(file_path)
            
            try:
                msh = message.segment('MSH')
                message_type_field = str(msh[9]) if len(msh) > 9 else ""
                
                # Extract message type (e.g., "ADT^A04" -> "ADT")
                message_type = message_type_field.split('^')[0] if '^' in message_type_field else message_type_field
                
                if message_type in self.required_segments:
                    required_segs = self.required_segments[message_type]
                    missing_segments = []
                    
                    for seg_name in required_segs:
                        try:
                            message.segment(seg_name)
                        except KeyError:
                            missing_segments.append(seg_name)
                    
                    if missing_segments:
                        failed_files.append({
                            'file': str(file_path),
                            'message_type': message_type,
                            'missing_segments': missing_segments
                        })
                        
            except Exception as e:
                failed_files.append({
                    'file': str(file_path),
                    'error': f"Failed to validate required segments: {str(e)}"
                })
        
        self.assertEqual(
            len(failed_files), 0,
            f"Files missing required segments: {json.dumps(failed_files, indent=2)}"
        )
    
    def test_processing_id_compliance(self):
        """Test that processing ID is set to 'P' for production"""
        if not self.hl7_files:
            self.skipTest("No HL7 files found to test")
        
        failed_files = []
        expected_processing_id = "P"
        
        for file_path in self.hl7_files:
            message, _ = self._parse_hl7_file(file_path)
            
            try:
                msh = message.segment('MSH')
                processing_id = str(msh[11]) if len(msh) > 11 else ""
                
                if processing_id != expected_processing_id:
                    failed_files.append({
                        'file': str(file_path),
                        'expected': expected_processing_id,
                        'actual': processing_id
                    })
                    
            except Exception as e:
                failed_files.append({
                    'file': str(file_path),
                    'error': f"Failed to check processing ID: {str(e)}"
                })
        
        self.assertEqual(
            len(failed_files), 0,
            f"Files with incorrect processing ID: {json.dumps(failed_files, indent=2)}"
        )


if __name__ == '__main__':
    unittest.main()
