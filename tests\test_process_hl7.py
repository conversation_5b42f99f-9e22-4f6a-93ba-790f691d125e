"""
Unit tests for processHl7.py - Main HL7 processing engine
"""

import unittest
import tempfile
import shutil
import os
import json
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open
import hl7

# Import the module under test
import sys
sys.path.append('.')
from processHl7 import HL7Processor, HL7ProcessingError


class TestHL7Processor(unittest.TestCase):
    """Test suite for HL7Processor class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.source_dir = os.path.join(self.temp_dir, 'source')
        self.output_dir = os.path.join(self.temp_dir, 'output')
        os.makedirs(self.source_dir)
        os.makedirs(self.output_dir)
        
        # Sample HL7 message content
        self.sample_hl7_content = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.5\r"
            "EVN|A04|202411101127|||123456\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F|||123 Main St^^Hometown^CA^12345^USA||(123)456-7890|||S|C|123456789\r"
            "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr.|||||||||||1234567890"
        )
        
        # Create sample mapping rules
        self.sample_mapping_rules = {
            "QATAR_ID_EXP": {
                "target_segment": "PID",
                "target_field": "3.8",
                "description": "Qatar ID Expiration Date"
            },
            "FAMILY_PHYSICIAN": {
                "target_segment": "ROL",
                "target_field": "4",
                "description": "Family Physician"
            }
        }
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def test_processor_initialization(self):
        """Test HL7Processor initialization"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        
        self.assertEqual(processor.source_dir, Path(self.source_dir))
        self.assertEqual(processor.output_dir, Path(self.output_dir))
        self.assertFalse(processor.verbose)
        self.assertIsInstance(processor.stats, dict)
        self.assertEqual(processor.stats['files_processed'], 0)
    
    def test_processor_initialization_with_verbose(self):
        """Test HL7Processor initialization with verbose mode"""
        processor = HL7Processor(self.source_dir, self.output_dir, verbose=True)
        self.assertTrue(processor.verbose)
    
    @patch('processHl7.Path.mkdir')
    def test_create_directories(self, mock_mkdir):
        """Test directory creation"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        processor.create_directories()
        
        # Verify mkdir was called for output and quarantine directories
        self.assertTrue(mock_mkdir.called)
    
    def test_find_hl7_files(self):
        """Test finding HL7 files in source directory"""
        # Create test HL7 files
        test_file1 = os.path.join(self.source_dir, 'test1.hl7')
        test_file2 = os.path.join(self.source_dir, 'subdir', 'test2.hl7')
        os.makedirs(os.path.dirname(test_file2))
        
        with open(test_file1, 'w') as f:
            f.write(self.sample_hl7_content)
        with open(test_file2, 'w') as f:
            f.write(self.sample_hl7_content)
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        hl7_files = processor.find_hl7_files()
        
        self.assertEqual(len(hl7_files), 2)
        self.assertTrue(any('test1.hl7' in str(f) for f in hl7_files))
        self.assertTrue(any('test2.hl7' in str(f) for f in hl7_files))
    
    def test_find_hl7_files_empty_directory(self):
        """Test finding HL7 files in empty directory"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        hl7_files = processor.find_hl7_files()
        self.assertEqual(len(hl7_files), 0)
    
    def test_parse_hl7_message_valid(self):
        """Test parsing valid HL7 message"""
        test_file = os.path.join(self.source_dir, 'test.hl7')
        with open(test_file, 'w') as f:
            f.write(self.sample_hl7_content)
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        message = processor.parse_hl7_message(Path(test_file))
        
        self.assertIsInstance(message, hl7.Message)
        msh = message.segment('MSH')
        self.assertEqual(str(msh[3]), 'SendingApp')
    
    def test_parse_hl7_message_empty_file(self):
        """Test parsing empty HL7 file"""
        test_file = os.path.join(self.source_dir, 'empty.hl7')
        with open(test_file, 'w') as f:
            f.write('')
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        
        with self.assertRaises(HL7ProcessingError) as context:
            processor.parse_hl7_message(Path(test_file))
        
        self.assertIn("Empty file", str(context.exception))
    
    def test_apply_hl7_enhancements(self):
        """Test applying HL7 enhancements"""
        test_file = os.path.join(self.source_dir, 'test.hl7')
        with open(test_file, 'w') as f:
            f.write(self.sample_hl7_content)
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        message = processor.parse_hl7_message(Path(test_file))
        enhanced_message = processor.apply_hl7_enhancements(message)
        
        msh = enhanced_message.segment('MSH')
        self.assertEqual(str(msh[12]), '2.8')  # Version updated to 2.8
        self.assertEqual(str(msh[11]), 'P')    # Processing ID set to P
    
    def test_ensure_evn_segment_exists(self):
        """Test ensuring EVN segment exists for ADT messages"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        processor.current_message_type = 'ADT'
        
        # Create message without EVN segment
        content_without_evn = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.5\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F"
        )
        message = hl7.parse(content_without_evn)
        
        processor.ensure_evn_segment(message)
        
        # Verify EVN segment was added
        try:
            evn_segment = message.segment('EVN')
            self.assertIsNotNone(evn_segment)
        except KeyError:
            self.fail("EVN segment was not added")
    
    def test_ensure_evn_segment_already_exists(self):
        """Test ensuring EVN segment when it already exists"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        processor.current_message_type = 'ADT'
        
        message = hl7.parse(self.sample_hl7_content)
        original_evn = str(message.segment('EVN'))
        
        processor.ensure_evn_segment(message)
        
        # Verify EVN segment wasn't modified
        current_evn = str(message.segment('EVN'))
        self.assertEqual(original_evn, current_evn)
    
    @patch('processHl7.json.load')
    @patch('builtins.open', new_callable=mock_open)
    def test_load_config(self, mock_file, mock_json_load):
        """Test loading configuration from mapping_rules.json"""
        mock_json_load.return_value = self.sample_mapping_rules
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        processor.load_config()
        
        self.assertEqual(processor.mapping_rules, self.sample_mapping_rules)
    
    @patch('processHl7.json.load')
    @patch('builtins.open', side_effect=FileNotFoundError)
    def test_load_config_file_not_found(self, mock_file, mock_json_load):
        """Test loading configuration when file doesn't exist"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        processor.load_config()
        
        # Should use default empty mapping rules
        self.assertEqual(processor.mapping_rules, {})
    
    def test_save_enhanced_message(self):
        """Test saving enhanced message"""
        test_file = os.path.join(self.source_dir, 'test.hl7')
        with open(test_file, 'w') as f:
            f.write(self.sample_hl7_content)
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        message = processor.parse_hl7_message(Path(test_file))
        enhanced_message = processor.apply_hl7_enhancements(message)
        
        processor.save_enhanced_message(enhanced_message, Path(test_file))
        
        # Verify output file was created
        output_file = os.path.join(self.output_dir, 'test.hl7')
        self.assertTrue(os.path.exists(output_file))
        
        # Verify content
        with open(output_file, 'r') as f:
            content = f.read()
            self.assertIn('2.8', content)  # Version should be updated
    
    def test_process_file_success(self):
        """Test successful file processing"""
        test_file = os.path.join(self.source_dir, 'test.hl7')
        with open(test_file, 'w') as f:
            f.write(self.sample_hl7_content)
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        result = processor.process_file(Path(test_file))
        
        self.assertTrue(result)
        self.assertEqual(processor.stats['files_enhanced'], 1)
    
    def test_process_file_failure(self):
        """Test file processing failure"""
        test_file = os.path.join(self.source_dir, 'invalid.hl7')
        with open(test_file, 'w') as f:
            f.write('Invalid HL7 content')
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        result = processor.process_file(Path(test_file))
        
        self.assertFalse(result)
        self.assertEqual(processor.stats['errors_encountered'], 1)
    
    def test_quarantine_file(self):
        """Test quarantining problematic files"""
        test_file = os.path.join(self.source_dir, 'problem.hl7')
        with open(test_file, 'w') as f:
            f.write('Problematic content')
        
        processor = HL7Processor(self.source_dir, self.output_dir)
        error = HL7ProcessingError("Test error", "TEST_ERROR", str(test_file))
        
        processor.quarantine_file(Path(test_file), error)
        
        # Verify quarantine directory and files exist
        quarantine_file = processor.quarantine_dir / 'problem.hl7'
        error_file = processor.quarantine_dir / 'problem.error.json'
        
        self.assertTrue(quarantine_file.exists())
        self.assertTrue(error_file.exists())
    
    def test_get_message_type_adt(self):
        """Test message type detection for ADT messages"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        message = hl7.parse(self.sample_hl7_content)
        
        msg_type = processor.get_message_type(message)
        self.assertEqual(msg_type, 'ADT')
    
    def test_get_message_type_unknown(self):
        """Test message type detection for unknown messages"""
        processor = HL7Processor(self.source_dir, self.output_dir)
        
        # Create message with unknown type
        unknown_content = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||XYZ^Z99|123456|P|2.5\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms."
        )
        message = hl7.parse(unknown_content)
        
        msg_type = processor.get_message_type(message)
        self.assertEqual(msg_type, 'UNKNOWN')


class TestHL7ProcessingError(unittest.TestCase):
    """Test suite for HL7ProcessingError exception class"""
    
    def test_error_creation(self):
        """Test creating HL7ProcessingError"""
        error = HL7ProcessingError("Test message", "TEST_CODE", "test_file.hl7")
        
        self.assertEqual(str(error), "Test message")
        self.assertEqual(error.error_code, "TEST_CODE")
        self.assertEqual(error.file_path, "test_file.hl7")
    
    def test_error_creation_without_file(self):
        """Test creating HL7ProcessingError without file path"""
        error = HL7ProcessingError("Test message", "TEST_CODE")
        
        self.assertEqual(str(error), "Test message")
        self.assertEqual(error.error_code, "TEST_CODE")
        self.assertIsNone(error.file_path)


if __name__ == '__main__':
    unittest.main()
