#!/usr/bin/env python3
"""
Fix specific HL7 files that need EVN segments and MSH field corrections
"""

import os
import shutil

def fix_file(file_path):
    """Fix a specific HL7 file"""
    print(f"Fixing {file_path}...")
    
    # Create backup
    backup_path = file_path + ".backup"
    shutil.copy2(file_path, backup_path)
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.strip().split('\n')
    
    # Fix MSH segment - remove duplicate 2.8 in MSH-13
    for i, line in enumerate(lines):
        if line.startswith('MSH|'):
            fields = line.split('|')
            if len(fields) > 13 and fields[13] == '2.8':
                # Remove the duplicate 2.8 from MSH-13
                fields[13] = ''
                lines[i] = '|'.join(fields)
            break
    
    # Check if EVN segment exists
    has_evn = any(line.strip().startswith('EVN|') for line in lines if line.strip())
    
    if not has_evn:
        # Add EVN segment after MSH
        for i, line in enumerate(lines):
            if line.startswith('MSH|'):
                # Extract timestamp from MSH-7
                fields = line.split('|')
                timestamp = fields[7] if len(fields) > 7 else "20240902082341"
                # Create EVN segment
                evn_segment = f"EVN|A04|{timestamp}||||"
                lines.insert(i + 1, evn_segment)
                break
    
    # Write fixed content
    fixed_content = '\n'.join(lines) + '\n'
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed {file_path}")

def main():
    """Main function"""
    files_to_fix = [
        "enhancedHl7/ADT-A04_115.hl7",
        "enhancedHl7/ADT-A04_116.hl7"
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            fix_file(file_path)
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
