#!/usr/bin/env python3
"""
Fix specific HL7 files that need EVN segments and encoding character corrections
"""

import os
import shutil
import glob

def fix_file(file_path):
    """Fix a specific HL7 file"""
    print(f"Fixing {file_path}...")

    # Create backup
    backup_path = file_path + ".backup"
    shutil.copy2(file_path, backup_path)

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    lines = content.strip().split('\n')
    fixed = False

    # Fix MSH segment encoding characters and other issues
    for i, line in enumerate(lines):
        if line.startswith('MSH|'):
            fields = line.split('|')

            # Fix encoding characters in MSH.2 (should be ^~\&)
            if len(fields) > 2:
                current_encoding = fields[2]
                if current_encoding != '^~\\&':
                    print(f"  Fixing encoding characters: '{current_encoding}' -> '^~\\&'")
                    fields[2] = '^~\\&'
                    fixed = True

            # Remove duplicate 2.8 in MSH-13 if present
            if len(fields) > 13 and fields[13] == '2.8':
                fields[13] = ''
                fixed = True

            # Update the line
            lines[i] = '|'.join(fields)
            break

    # Check if EVN segment exists for ADT messages
    has_evn = any(line.strip().startswith('EVN|') for line in lines if line.strip())

    if not has_evn:
        # Check if this is an ADT message
        for i, line in enumerate(lines):
            if line.startswith('MSH|'):
                fields = line.split('|')
                if len(fields) > 9 and fields[9].startswith('ADT^'):
                    # Extract timestamp from MSH-7
                    timestamp = fields[7] if len(fields) > 7 else "20240902082341"
                    # Extract event type from MSH-9 (e.g., ADT^A04 -> A04)
                    event_type = "A04"  # Default
                    if '^' in fields[9]:
                        parts = fields[9].split('^')
                        if len(parts) > 1:
                            event_type = parts[1]

                    # Create EVN segment
                    evn_segment = f"EVN|{event_type}|{timestamp}||||"
                    lines.insert(i + 1, evn_segment)
                    print(f"  Added missing EVN segment: {evn_segment}")
                    fixed = True
                break

    # Write fixed content only if changes were made
    if fixed:
        fixed_content = '\n'.join(lines) + '\n'
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        print(f"  Successfully fixed {file_path}")
    else:
        print(f"  No fixes needed for {file_path}")
        # Remove backup if no changes were made
        if os.path.exists(backup_path):
            os.remove(backup_path)

def main():
    """Main function"""
    # Fix the specific files mentioned in test failures
    specific_files = [
        "enhancedHl7/ADT-A04/ADT-A04_PATID1234-123456789-PSSN123121234-1234567-PATID567-_MSG00001_606d4303-a634-4b97-b21e-f5af8a19533b.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08529312_Q7486688090T13603923582_186e5567-1c89-4446-add5-4685778fc0c4.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08529312_Q7486688090T13603923582_6d6a77fa-d5a4-43da-8cb0-7386a8640bcd.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_0c16a873-f3cc-4acb-9047-432b70c204c8.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_ba184666-3364-4544-a885-3073b4d3d11a.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_c00e2873-d8a7-4821-b5fa-15500ebf4821.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_d53c6ea3-dc58-41cd-9aa9-ea93b4f6c373.hl7"
    ]

    print("Fixing specific files mentioned in test failures...")
    for file_path in specific_files:
        if os.path.exists(file_path):
            fix_file(file_path)
        else:
            print(f"File not found: {file_path}")

    print("\nScanning for additional files that might need fixes...")
    # Also scan all HL7 files for similar issues
    hl7_pattern = "enhancedHl7/**/*.hl7"
    all_files = glob.glob(hl7_pattern, recursive=True)

    files_with_issues = []
    for file_path in all_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            lines = content.strip().split('\n')
            needs_fix = False

            # Check for encoding character issues
            for line in lines:
                if line.startswith('MSH|'):
                    fields = line.split('|')
                    if len(fields) > 2 and fields[2] != '^~\\&':
                        needs_fix = True
                        break

            # Check for missing EVN in ADT messages
            if not needs_fix:
                has_evn = any(line.strip().startswith('EVN|') for line in lines if line.strip())
                if not has_evn:
                    for line in lines:
                        if line.startswith('MSH|'):
                            fields = line.split('|')
                            if len(fields) > 9 and fields[9].startswith('ADT^'):
                                needs_fix = True
                                break

            if needs_fix and file_path not in specific_files:
                files_with_issues.append(file_path)

        except Exception as e:
            print(f"Error checking {file_path}: {e}")

    if files_with_issues:
        print(f"\nFound {len(files_with_issues)} additional files that need fixes:")
        for file_path in files_with_issues[:10]:  # Show first 10
            fix_file(file_path)

        if len(files_with_issues) > 10:
            print(f"\n... and {len(files_with_issues) - 10} more files need fixes.")
            print("Run the script again to fix all remaining files.")
    else:
        print("\nNo additional files found that need fixes.")

if __name__ == "__main__":
    main()
