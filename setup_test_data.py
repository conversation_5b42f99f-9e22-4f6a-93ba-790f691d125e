#!/usr/bin/env python3
"""
Setup script to create test data for HL7 Enhancement Python Tool tests
This script creates sample HL7 files and directory structure needed for testing
"""

import os
import json
from pathlib import Path


def create_sample_hl7_files():
    """Create sample HL7 files for testing"""
    
    # Sample HL7 messages
    sample_messages = {
        "ADT_A04_sample.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\r"
            "EVN|A04|202411101127|||123456\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F|||123 Main St^^Hometown^CA^12345^USA||(123)456-7890|||S|C|123456789\r"
            "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr.|||||||||||1234567890"
        ),
        "ADT_A08_sample.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101128||ADT^A08|123457|P|2.8\r"
            "EVN|A08|202411101128|||123457\r"
            "PID|1||123457^^^Hospital^MR||Smith^John^B^^Mr.||19750615|M|||456 Oak Ave^^Cityville^NY^54321^USA||(555)123-4567|||M|C|987654321\r"
            "PV1|1|O|ER^02^01||||67890^Johnson^Mary^C^^Dr.|||||||||||9876543210"
        ),
        "ORU_R01_sample.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101129||ORU^R01|123458|P|2.8\r"
            "PID|1||123458^^^Hospital^MR||Brown^Alice^C^^Mrs.||19820315|F|||789 Pine St^^Townsville^TX^67890^USA||(444)555-6666|||S|C|456789123\r"
            "OBR|1|123458|789012|CBC^Complete Blood Count||202411101129|||||||||||67890^Johnson^Mary^C^^Dr.\r"
            "OBX|1|NM|WBC^White Blood Cell Count|1|7.5|10*3/uL|4.0-11.0|N|||F\r"
            "OBX|2|NM|RBC^Red Blood Cell Count|1|4.2|10*6/uL|3.8-5.2|N|||F\r"
            "OBX|3|NM|HGB^Hemoglobin|1|13.5|g/dL|12.0-16.0|N|||F"
        ),
        "SIU_S12_sample.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101130||SIU^S12|123459|P|2.8\r"
            "SCH|123459|||||ROUTINE^Routine Appointment|SCHEDULED^Scheduled|30|MIN|202411151400|202411151430\r"
            "PID|1||123459^^^Hospital^MR||Wilson^Robert^D^^Mr.||19700920|M|||321 Elm St^^Villagetown^FL^13579^USA||(777)888-9999|||M|C|789123456\r"
            "AIG|1|SCHEDULED^Scheduled|12345^Smith^John^A^^Dr.|202411151400|202411151430|30|MIN"
        ),
        "MDM_T02_sample.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101131||MDM^T02|123460|P|2.8\r"
            "EVN|T02|202411101131|||123460\r"
            "PID|1||123460^^^Hospital^MR||Davis^Mary^E^^Ms.||19850710|F|||654 Maple Ave^^Riverside^CA^90210^USA||(310)555-1234|||S|C|321654987\r"
            "TXA|1|CN|TX|202411101131|12345^Smith^John^A^^Dr.|202411101131|202411101131|AU|AV|LA|AV|AV|Discharge Summary|123460|AV||AV|F"
        )
    }
    
    # Create enhancedHl7 directory
    enhanced_dir = Path("enhancedHl7")
    enhanced_dir.mkdir(exist_ok=True)
    
    # Create rawhl7messages directory
    raw_dir = Path("rawhl7messages")
    raw_dir.mkdir(exist_ok=True)
    
    # Create subdirectories for organization
    subdirs = [
        "admissions",
        "lab_results", 
        "appointments",
        "documents"
    ]
    
    for subdir in subdirs:
        (enhanced_dir / subdir).mkdir(exist_ok=True)
        (raw_dir / subdir).mkdir(exist_ok=True)
    
    # Create sample files in both directories
    for filename, content in sample_messages.items():
        # Determine subdirectory based on message type
        if "ADT" in filename:
            subdir = "admissions"
        elif "ORU" in filename:
            subdir = "lab_results"
        elif "SIU" in filename:
            subdir = "appointments"
        elif "MDM" in filename:
            subdir = "documents"
        else:
            subdir = ""
        
        # Create files in enhanced directory
        if subdir:
            enhanced_file = enhanced_dir / subdir / filename
        else:
            enhanced_file = enhanced_dir / filename
        
        with open(enhanced_file, 'w') as f:
            f.write(content)
        
        # Create files in raw directory (with older version for processing)
        raw_content = content.replace("|2.8", "|2.5")  # Simulate older version
        if subdir:
            raw_file = raw_dir / subdir / filename
        else:
            raw_file = raw_dir / filename
        
        with open(raw_file, 'w') as f:
            f.write(raw_content)
    
    print(f"✅ Created {len(sample_messages)} sample HL7 files in enhancedHl7/ and rawhl7messages/")
    return len(sample_messages)


def create_mapping_rules():
    """Create sample mapping rules file"""
    mapping_rules = {
        "QATAR_ID_EXP": {
            "target_segment": "PID",
            "target_field": "3.8",
            "description": "Qatar ID Expiration Date"
        },
        "HC_EXP_DATE": {
            "target_segment": "PID", 
            "target_field": "3.8",
            "description": "Health Card Expiration Date"
        },
        "FAMILY_PHYSICIAN": {
            "target_segment": "ROL",
            "target_field": "4", 
            "description": "Family Physician"
        },
        "PRIM_ORG_NAME": {
            "target_segment": "PD1",
            "target_field": "3",
            "description": "Primary Organization Name"
        },
        "EMERGENCY_CONTACT": {
            "target_segment": "NK1",
            "target_field": "2",
            "description": "Emergency Contact Name"
        }
    }
    
    with open("mapping_rules.json", 'w') as f:
        json.dump(mapping_rules, f, indent=2)
    
    print("✅ Created mapping_rules.json")


def create_invalid_test_files():
    """Create invalid HL7 files for error testing"""
    test_data_dir = Path("test_data")
    test_data_dir.mkdir(exist_ok=True)
    
    invalid_files = {
        "invalid_no_msh.hl7": (
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F\r"
            "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr."
        ),
        "invalid_wrong_version.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.3\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F"
        ),
        "invalid_missing_segments.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F"
        ),
        "invalid_malformed.hl7": (
            "MSH|^~\\&|SendingApp|SendingFac\r"
            "This is not a valid HL7 message\r"
            "Random text here"
        ),
        "empty_file.hl7": ""
    }
    
    for filename, content in invalid_files.items():
        file_path = test_data_dir / filename
        with open(file_path, 'w') as f:
            f.write(content)
    
    print(f"✅ Created {len(invalid_files)} invalid test files in test_data/")
    return len(invalid_files)


def create_large_test_dataset():
    """Create a larger dataset for performance testing"""
    large_data_dir = Path("large_test_data")
    large_data_dir.mkdir(exist_ok=True)
    
    # Base message template
    base_message = (
        "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|{timestamp}||ADT^A04|{msg_id}|P|2.8\r"
        "EVN|A04|{timestamp}|||{msg_id}\r"
        "PID|1||{patient_id}^^^Hospital^MR||{last_name}^{first_name}^A^^{title}||{dob}|{gender}|||{address}||(123)456-7890|||S|C|{ssn}\r"
        "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr.|||||||||||{visit_id}"
    )
    
    # Generate 100 test files
    for i in range(1, 101):
        filename = f"ADT_A04_{i:03d}.hl7"
        
        # Generate varied data
        patient_id = f"PAT{i:06d}"
        msg_id = f"MSG{i:06d}"
        visit_id = f"VIS{i:06d}"
        ssn = f"{i:09d}"
        
        # Vary other fields
        first_names = ["John", "Jane", "Alice", "Bob", "Carol", "David", "Emma", "Frank"]
        last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis"]
        titles = ["Mr.", "Ms.", "Mrs.", "Dr."]
        genders = ["M", "F"]
        
        first_name = first_names[i % len(first_names)]
        last_name = last_names[i % len(last_names)]
        title = titles[i % len(titles)]
        gender = genders[i % len(genders)]
        
        # Generate DOB (1950-2000)
        year = 1950 + (i % 50)
        month = (i % 12) + 1
        day = (i % 28) + 1
        dob = f"{year:04d}{month:02d}{day:02d}"
        
        # Generate timestamp
        timestamp = f"20241110{(1100 + i):04d}"
        
        # Generate address
        address = f"{i} Main St^^Hometown^CA^{12345 + i}^USA"
        
        content = base_message.format(
            timestamp=timestamp,
            msg_id=msg_id,
            patient_id=patient_id,
            last_name=last_name,
            first_name=first_name,
            title=title,
            dob=dob,
            gender=gender,
            address=address,
            ssn=ssn,
            visit_id=visit_id
        )
        
        file_path = large_data_dir / filename
        with open(file_path, 'w') as f:
            f.write(content)
    
    print("✅ Created 100 large test dataset files in large_test_data/")
    return 100


def main():
    """Main setup function"""
    print("🚀 Setting up test data for HL7 Enhancement Python Tool")
    print("=" * 60)
    
    total_files = 0
    
    # Create sample HL7 files
    total_files += create_sample_hl7_files()
    
    # Create mapping rules
    create_mapping_rules()
    
    # Create invalid test files
    total_files += create_invalid_test_files()
    
    # Create large test dataset
    total_files += create_large_test_dataset()
    
    print("=" * 60)
    print(f"✅ Test data setup complete!")
    print(f"📊 Total files created: {total_files}")
    print()
    print("📁 Directory structure created:")
    print("   ├── enhancedHl7/")
    print("   │   ├── admissions/")
    print("   │   ├── lab_results/")
    print("   │   ├── appointments/")
    print("   │   └── documents/")
    print("   ├── rawhl7messages/")
    print("   │   ├── admissions/")
    print("   │   ├── lab_results/")
    print("   │   ├── appointments/")
    print("   │   └── documents/")
    print("   ├── test_data/")
    print("   ├── large_test_data/")
    print("   └── mapping_rules.json")
    print()
    print("🧪 You can now run the test suite:")
    print("   python tests/test_runner.py")
    print()
    print("🔍 Or run individual test modules:")
    print("   python -m unittest tests.test_hl7_validation")
    print("   python -m unittest tests.test_process_hl7")
    print("   python -m unittest tests.test_validator")


if __name__ == "__main__":
    main()
