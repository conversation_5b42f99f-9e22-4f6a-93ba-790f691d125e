# HL7 Enhancement Python Tool

A comprehensive Python-based HL7 message processing and enhancement engine designed for healthcare data standardization and validation. This tool processes HL7 v2.x messages and upgrades them to HL7 v2.8 standards while applying configurable data transformations.

## 🚀 Overview

This project provides a robust solution for healthcare organizations to process, enhance, and validate HL7 messages at scale. It features automated batch processing, configurable data mapping, comprehensive logging, and built-in validation capabilities.

**Reference**: [HL7 Definition Guide](https://hl7-definition.caristix.com/)

## ✨ Key Features

### 🔄 Automated Batch Processing
- **Recursive Directory Scanning**: Automatically discovers all `.hl7` files in the source directory tree
- **Structure Preservation**: Maintains original folder hierarchy in output directory
- **Batch Processing**: Handles hundreds of files efficiently with progress tracking
- **Example**: `rawhl7messages/lab1/msg.hl7` → `enhancedHl7/lab1/msg.hl7`

### 🧬 Advanced HL7 Parsing & Enhancement
- **Robust Parsing**: Uses `python-hl7` library with intelligent line-ending detection
- **HL7 v2.8 Standardization**:
  - Updates MSH-12 (Version ID) to "2.8"
  - Sets MSH-11 (Processing ID) to "P" (Production)
  - Applies HL7 v2.8 compliance standards
- **Configurable OBX Segment Remapping**:
  - Dynamic data transformation based on `mapping_rules.json`
  - Moves specific observation data to appropriate patient/provider segments
  - **Current Mapping Rules**:
    - `QATAR_ID_EXP` → `PID-3.8` (ID Expiration Date)
    - `HC EXP DATE` → `PID-3.8` (Health Card Expiration)
    - `FAMILY_PHYSICIAN` → `ROL-4` (Provider Role)
    - `PRIM_ORG_NAME` → `PD1-3` (Primary Facility)

### 📊 Comprehensive Logging & Monitoring
- **Activity Logging**: Human-readable processing logs (`processing_activity.log`)
- **Error Tracking**: JSON-formatted error details (`error_details.log`)
- **Real-time Statistics**: Files processed, enhanced, and error counts
- **Quarantine System**: Automatic isolation of problematic files with detailed error context

### ⚙️ Flexible Configuration
- **JSON-based Rules**: Easy modification of mapping rules without code changes
- **Command-line Interface**: Customizable source and output directories
- **Verbose Logging**: Optional detailed processing information
- **Error Handling**: Graceful handling of file access and parsing errors

## 📁 Project Structure

```
Hl7EnhancePython/
├── processHl7.py          # Main HL7 processing engine
├── validator.py           # HL7 v2.8 validation tool
├── hl7tomd.py            # HL7 to Markdown converter utility
├── mapping_rules.json    # OBX segment mapping configuration
├── requirements.txt      # Python dependencies
├── rawhl7messages/       # Input directory for raw HL7 files
├── enhancedHl7/         # Output directory for processed files
├── quarantine/          # Failed files with error details
├── processing_activity.log  # Processing activity log
└── error_details.log    # Detailed error information
```

## 🛠️ Installation

### Prerequisites
- Python 3.7 or higher
- pip package manager

### Setup
1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Hl7EnhancePython
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**:
   ```bash
   python processHl7.py --help
   ```

## 🚀 Usage

### Basic Processing
Process all HL7 files in the default directories:
```bash
python processHl7.py
```

### Custom Directories
Specify custom source and output directories:
```bash
python processHl7.py --source /path/to/raw/files --output /path/to/enhanced/files
```

### Verbose Mode
Enable detailed logging for troubleshooting:
```bash
python processHl7.py --verbose
```

### Validation
Validate processed HL7 files against v2.8 standards:
```bash
python validator.py
```

### Convert to Markdown
Convert HL7 files to Markdown format for documentation:
```bash
python hl7tomd.py
```

## ⚙️ Configuration

### Mapping Rules (`mapping_rules.json`)
Configure how OBX segments are remapped to other HL7 segments:

```json
[
  {
    "obxField": "OBX-3.1",
    "obxValue": "QATAR_ID_EXP",
    "targetSegment": "PID",
    "targetField": "3.8",
    "removeOriginal": true,
    "description": "Map Qatar ID expiration date from OBX to PID-3.8"
  }
]
```

**Configuration Parameters**:
- `obxField`: Source field in OBX segment (typically "OBX-3.1")
- `obxValue`: Observation identifier to match
- `targetSegment`: Destination HL7 segment (PID, ROL, PD1, etc.)
- `targetField`: Destination field within the segment
- `removeOriginal`: Whether to remove the original OBX segment
- `description`: Human-readable description of the mapping

## 📋 Dependencies

The project requires the following Python packages:

```txt
hl7              # Core HL7 message parsing and manipulation
hl7apy           # Additional HL7 processing capabilities
python-dotenv    # Environment variable management
requests         # HTTP client for external API calls
```

Install all dependencies with:
```bash
pip install -r requirements.txt
```

## 🔍 Processing Workflow

1. **Discovery**: Recursively scans source directory for `.hl7` files
2. **Parsing**: Loads and parses each HL7 message using intelligent format detection
3. **Enhancement**:
   - Updates message version to HL7 v2.8
   - Sets processing ID to Production
   - Applies OBX segment remapping based on configuration
4. **Validation**: Ensures message integrity and compliance
5. **Output**: Saves enhanced messages preserving directory structure
6. **Error Handling**: Quarantines problematic files with detailed error information

## 📊 Output and Logging

### Processing Logs
- **`processing_activity.log`**: Human-readable processing activity
- **`error_details.log`**: JSON-formatted error details for automated analysis

### Directory Structure
```
enhancedHl7/          # Successfully processed files
quarantine/           # Failed files with error context
├── filename.hl7      # Original problematic file
└── filename.error.json  # Detailed error information
```

### Statistics Tracking
The processor tracks and reports:
- Total files discovered
- Files successfully processed
- Files enhanced with transformations
- Errors encountered
- Files quarantined

## 🧪 Validation Features

The `validator.py` script provides comprehensive HL7 v2.8 validation:

- **Version Compliance**: Ensures MSH-12 field contains "2.8"
- **Message Structure**: Validates required segments for message types
- **Field Validation**: Checks MSH segment field completeness
- **Processing Standards**: Verifies production-ready message formatting

### Validation Report
Generates detailed reports including:
- Valid/invalid file counts
- Specific validation errors
- Processing time statistics
- Summary of compliance issues

## 🔧 Utilities

### HL7 to Markdown Converter (`hl7tomd.py`)
Converts HL7 files to Markdown format for:
- Documentation purposes
- Human-readable message review
- Batch compilation with configurable message limits
- Duplicate detection and removal

**Features**:
- Processes up to 25 messages per Markdown file
- Removes duplicate messages based on content
- Preserves source file information
- Creates organized, readable documentation

## 🚨 Error Handling

### Quarantine System
Files that fail processing are automatically moved to the `quarantine/` directory with:
- Original file preserved
- Detailed error information in JSON format
- Error categorization (parsing, processing, file access)
- Stack traces for debugging

### Error Categories
- **HL7_PARSING_FAILURE**: Invalid HL7 message structure
- **PROCESSING_ERROR**: Enhancement operation failures
- **FILE_READ_ERROR**: File access or permission issues
- **CONFIG_ERROR**: Configuration file problems
- **DIRECTORY_ERROR**: Directory creation or access issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues, questions, or contributions:
1. Check existing issues in the repository
2. Create a new issue with detailed description
3. Include sample HL7 files (anonymized) when reporting parsing issues
4. Provide error logs and configuration details

## 🔮 Future Enhancements

- Support for additional HL7 versions (v2.5, v2.7)
- Web-based configuration interface
- Real-time processing monitoring dashboard
- Integration with healthcare data pipelines
- Advanced validation rule customization
- Performance optimization for large-scale processing

---

**Note**: This tool is designed for healthcare data processing. Ensure compliance with relevant data protection regulations (HIPAA, GDPR) when processing patient information.