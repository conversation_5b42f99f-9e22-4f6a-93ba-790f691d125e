"""
Unit tests for validator.py - HL7 validation tool
"""

import unittest
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
import hl7

# Import the module under test
import sys
sys.path.append('.')
import validator


class TestHL7Validator(unittest.TestCase):
    """Test suite for HL7 validator functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_folder = os.path.join(self.temp_dir, 'test_hl7')
        os.makedirs(self.test_folder)
        
        # Sample valid HL7 v2.8 message
        self.valid_hl7_content = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\r"
            "EVN|A04|202411101127|||123456\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F|||123 Main St^^Hometown^CA^12345^USA||(123)456-7890|||S|C|123456789\r"
            "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr.|||||||||||1234567890"
        )
        
        # Sample invalid HL7 message (wrong version)
        self.invalid_version_hl7 = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.5\r"
            "EVN|A04|202411101127|||123456\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F"
        )
        
        # Sample HL7 message missing required segments
        self.missing_segments_hl7 = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F"
        )
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def test_parse_hl7_file_valid(self):
        """Test parsing valid HL7 file"""
        test_file = os.path.join(self.test_folder, 'valid.hl7')
        with open(test_file, 'w') as f:
            f.write(self.valid_hl7_content)
        
        message, raw_content = validator.parse_hl7_file(test_file)
        
        self.assertIsInstance(message, hl7.Message)
        self.assertIsInstance(raw_content, str)
        self.assertIn('MSH', raw_content)
    
    def test_parse_hl7_file_invalid(self):
        """Test parsing invalid HL7 file"""
        test_file = os.path.join(self.test_folder, 'invalid.hl7')
        with open(test_file, 'w') as f:
            f.write('Invalid HL7 content')
        
        message, raw_content = validator.parse_hl7_file(test_file)
        
        self.assertIsNone(message)
        self.assertEqual(raw_content, 'Invalid HL7 content')
    
    def test_parse_hl7_file_empty(self):
        """Test parsing empty HL7 file"""
        test_file = os.path.join(self.test_folder, 'empty.hl7')
        with open(test_file, 'w') as f:
            f.write('')
        
        message, raw_content = validator.parse_hl7_file(test_file)
        
        self.assertIsNone(message)
        self.assertEqual(raw_content, '')
    
    def test_validate_hl7_version_valid(self):
        """Test validating correct HL7 version"""
        message = hl7.parse(self.valid_hl7_content)
        
        is_valid, error_msg = validator.validate_hl7_version(message, '2.8')
        
        self.assertTrue(is_valid)
        self.assertIsNone(error_msg)
    
    def test_validate_hl7_version_invalid(self):
        """Test validating incorrect HL7 version"""
        message = hl7.parse(self.invalid_version_hl7)
        
        is_valid, error_msg = validator.validate_hl7_version(message, '2.8')
        
        self.assertFalse(is_valid)
        self.assertIn('Expected version 2.8', error_msg)
    
    def test_validate_hl7_version_missing_msh(self):
        """Test validating HL7 version with missing MSH segment"""
        # Create message without MSH segment
        invalid_content = "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms."
        message = hl7.parse(invalid_content)
        
        is_valid, error_msg = validator.validate_hl7_version(message, '2.8')
        
        self.assertFalse(is_valid)
        self.assertIn('MSH segment not found', error_msg)
    
    def test_validate_msh_segment_valid(self):
        """Test validating valid MSH segment"""
        message = hl7.parse(self.valid_hl7_content)
        
        is_valid, errors = validator.validate_msh_segment(message)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_msh_segment_missing_fields(self):
        """Test validating MSH segment with missing fields"""
        # Create MSH with missing fields
        incomplete_msh = "MSH|^~\\&|SendingApp||||202411101127"
        message = hl7.parse(incomplete_msh)
        
        is_valid, errors = validator.validate_msh_segment(message)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_validate_required_segments_valid(self):
        """Test validating message with all required segments"""
        message = hl7.parse(self.valid_hl7_content)
        
        is_valid, missing_segments = validator.validate_required_segments(message, 'ADT')
        
        self.assertTrue(is_valid)
        self.assertEqual(len(missing_segments), 0)
    
    def test_validate_required_segments_missing(self):
        """Test validating message with missing required segments"""
        message = hl7.parse(self.missing_segments_hl7)
        
        is_valid, missing_segments = validator.validate_required_segments(message, 'ADT')
        
        self.assertFalse(is_valid)
        self.assertIn('EVN', missing_segments)
    
    def test_validate_required_segments_unknown_type(self):
        """Test validating message with unknown message type"""
        message = hl7.parse(self.valid_hl7_content)
        
        is_valid, missing_segments = validator.validate_required_segments(message, 'UNKNOWN')
        
        self.assertTrue(is_valid)  # Unknown types pass validation
        self.assertEqual(len(missing_segments), 0)
    
    def test_get_message_type_adt(self):
        """Test extracting ADT message type"""
        message = hl7.parse(self.valid_hl7_content)
        
        msg_type = validator.get_message_type(message)
        
        self.assertEqual(msg_type, 'ADT')
    
    def test_get_message_type_complex(self):
        """Test extracting message type from complex message type field"""
        # Create message with complex message type
        complex_content = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ORU^R01^ORU_R01|123456|P|2.8\r"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms."
        )
        message = hl7.parse(complex_content)
        
        msg_type = validator.get_message_type(message)
        
        self.assertEqual(msg_type, 'ORU')
    
    def test_get_message_type_missing_msh(self):
        """Test extracting message type with missing MSH segment"""
        invalid_content = "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms."
        message = hl7.parse(invalid_content)
        
        msg_type = validator.get_message_type(message)
        
        self.assertEqual(msg_type, 'UNKNOWN')
    
    def test_validate_single_file_valid(self):
        """Test validating single valid file"""
        test_file = os.path.join(self.test_folder, 'valid.hl7')
        with open(test_file, 'w') as f:
            f.write(self.valid_hl7_content)
        
        result = validator.validate_single_file(test_file, '2.8')
        
        self.assertTrue(result['is_valid'])
        self.assertEqual(len(result['errors']), 0)
        self.assertEqual(result['message_type'], 'ADT')
    
    def test_validate_single_file_invalid(self):
        """Test validating single invalid file"""
        test_file = os.path.join(self.test_folder, 'invalid.hl7')
        with open(test_file, 'w') as f:
            f.write(self.invalid_version_hl7)
        
        result = validator.validate_single_file(test_file, '2.8')
        
        self.assertFalse(result['is_valid'])
        self.assertGreater(len(result['errors']), 0)
    
    def test_validate_single_file_parse_error(self):
        """Test validating file with parse errors"""
        test_file = os.path.join(self.test_folder, 'parse_error.hl7')
        with open(test_file, 'w') as f:
            f.write('Completely invalid content')
        
        result = validator.validate_single_file(test_file, '2.8')
        
        self.assertFalse(result['is_valid'])
        self.assertIn('Failed to parse', result['errors'][0])
    
    @patch('validator.os.walk')
    def test_find_hl7_files(self, mock_walk):
        """Test finding HL7 files in directory"""
        mock_walk.return_value = [
            (self.test_folder, [], ['file1.hl7', 'file2.hl7', 'file3.txt'])
        ]
        
        hl7_files = validator.find_hl7_files(self.test_folder)
        
        self.assertEqual(len(hl7_files), 2)
        self.assertTrue(any('file1.hl7' in f for f in hl7_files))
        self.assertTrue(any('file2.hl7' in f for f in hl7_files))
    
    def test_find_hl7_files_empty_directory(self):
        """Test finding HL7 files in empty directory"""
        empty_dir = os.path.join(self.temp_dir, 'empty')
        os.makedirs(empty_dir)
        
        hl7_files = validator.find_hl7_files(empty_dir)
        
        self.assertEqual(len(hl7_files), 0)
    
    @patch('validator.validate_single_file')
    @patch('validator.find_hl7_files')
    def test_validate_folder(self, mock_find_files, mock_validate_file):
        """Test validating entire folder"""
        mock_find_files.return_value = ['file1.hl7', 'file2.hl7']
        mock_validate_file.side_effect = [
            {'is_valid': True, 'errors': [], 'message_type': 'ADT'},
            {'is_valid': False, 'errors': ['Version error'], 'message_type': 'ADT'}
        ]
        
        results = validator.validate_folder(self.test_folder, '2.8')
        
        self.assertEqual(len(results), 2)
        self.assertTrue(results[0]['is_valid'])
        self.assertFalse(results[1]['is_valid'])
    
    @patch('builtins.print')
    @patch('validator.validate_folder')
    def test_generate_validation_report(self, mock_validate_folder, mock_print):
        """Test generating validation report"""
        mock_validate_folder.return_value = [
            {'file_path': 'file1.hl7', 'is_valid': True, 'errors': [], 'message_type': 'ADT'},
            {'file_path': 'file2.hl7', 'is_valid': False, 'errors': ['Version error'], 'message_type': 'ADT'}
        ]
        
        validator.generate_validation_report(self.test_folder, '2.8')
        
        # Verify that print was called (report was generated)
        self.assertTrue(mock_print.called)
    
    def test_format_validation_errors(self):
        """Test formatting validation errors"""
        errors = ['Error 1', 'Error 2', 'Error 3']
        
        formatted = validator.format_validation_errors(errors)
        
        self.assertIn('Error 1', formatted)
        self.assertIn('Error 2', formatted)
        self.assertIn('Error 3', formatted)
    
    def test_calculate_validation_statistics(self):
        """Test calculating validation statistics"""
        results = [
            {'is_valid': True, 'message_type': 'ADT'},
            {'is_valid': True, 'message_type': 'ADT'},
            {'is_valid': False, 'message_type': 'ORU'},
            {'is_valid': False, 'message_type': 'ADT'}
        ]
        
        stats = validator.calculate_validation_statistics(results)
        
        self.assertEqual(stats['total_files'], 4)
        self.assertEqual(stats['valid_files'], 2)
        self.assertEqual(stats['invalid_files'], 2)
        self.assertEqual(stats['success_rate'], 50.0)


if __name__ == '__main__':
    unittest.main()
