"""
Comprehensive test runner for HL7 Enhancement Python Tool
Runs all test suites and generates detailed reports
"""

import unittest
import sys
import os
import time
from io import StringIO
from pathlib import Path
import json
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import all test modules
from tests.test_hl7_validation import TestHL7MessageValidation
from tests.test_process_hl7 import TestHL7Processor, TestHL7ProcessingError
from tests.test_validator import TestHL7Validator
from tests.test_hl7tomd import TestHL7ToMarkdownConverter
from tests.test_hl7_compliance_fixer import TestHL7ComplianceFixer


class TestResult:
    """Custom test result class to capture detailed test information"""
    
    def __init__(self):
        self.tests_run = 0
        self.failures = []
        self.errors = []
        self.skipped = []
        self.successes = []
        self.start_time = None
        self.end_time = None
    
    def start_test(self, test):
        """Called when a test starts"""
        self.start_time = time.time()
    
    def stop_test(self, test):
        """Called when a test ends"""
        self.end_time = time.time()
        self.tests_run += 1
    
    def add_success(self, test):
        """Called when a test passes"""
        self.successes.append({
            'test': str(test),
            'duration': self.end_time - self.start_time if self.start_time else 0
        })
    
    def add_error(self, test, err):
        """Called when a test has an error"""
        self.errors.append({
            'test': str(test),
            'error': str(err[1]),
            'traceback': err[2],
            'duration': self.end_time - self.start_time if self.start_time else 0
        })
    
    def add_failure(self, test, err):
        """Called when a test fails"""
        self.failures.append({
            'test': str(test),
            'failure': str(err[1]),
            'traceback': err[2],
            'duration': self.end_time - self.start_time if self.start_time else 0
        })
    
    def add_skip(self, test, reason):
        """Called when a test is skipped"""
        self.skipped.append({
            'test': str(test),
            'reason': str(reason)
        })


class ComprehensiveTestRunner:
    """Comprehensive test runner with detailed reporting"""
    
    def __init__(self, verbosity=2):
        self.verbosity = verbosity
        self.test_suites = {
            'HL7 Message Validation': TestHL7MessageValidation,
            'HL7 Processor': TestHL7Processor,
            'HL7 Processing Error': TestHL7ProcessingError,
            'HL7 Validator': TestHL7Validator,
            'HL7 to Markdown Converter': TestHL7ToMarkdownConverter,
            'HL7 Compliance Fixer': TestHL7ComplianceFixer
        }
        self.results = {}
        self.overall_start_time = None
        self.overall_end_time = None
    
    def run_all_tests(self):
        """Run all test suites"""
        print("=" * 80)
        print("HL7 ENHANCEMENT PYTHON TOOL - COMPREHENSIVE TEST SUITE")
        print("=" * 80)
        print(f"Starting test execution at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        self.overall_start_time = time.time()
        
        for suite_name, test_class in self.test_suites.items():
            print(f"Running {suite_name} tests...")
            print("-" * 60)
            
            # Create test suite
            suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
            
            # Run tests with custom result collector
            result = self._run_test_suite(suite, suite_name)
            self.results[suite_name] = result
            
            # Print immediate results
            self._print_suite_summary(suite_name, result)
            print()
        
        self.overall_end_time = time.time()
        
        # Generate comprehensive report
        self._generate_comprehensive_report()
        
        return self._calculate_overall_success()
    
    def _run_test_suite(self, suite, suite_name):
        """Run a single test suite and collect results"""
        # Capture stdout to avoid cluttering output
        old_stdout = sys.stdout
        sys.stdout = StringIO()
        
        try:
            # Create custom test runner
            runner = unittest.TextTestRunner(
                stream=sys.stdout,
                verbosity=self.verbosity,
                resultclass=unittest.TestResult
            )
            
            result = runner.run(suite)
            
            # Restore stdout
            sys.stdout = old_stdout
            
            return {
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'skipped': len(result.skipped),
                'successes': result.testsRun - len(result.failures) - len(result.errors),
                'failure_details': [{'test': str(test), 'message': str(err)} for test, err in result.failures],
                'error_details': [{'test': str(test), 'message': str(err)} for test, err in result.errors],
                'skip_details': [{'test': str(test), 'reason': str(reason)} for test, reason in result.skipped]
            }
        
        except Exception as e:
            sys.stdout = old_stdout
            return {
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'skipped': 0,
                'successes': 0,
                'failure_details': [],
                'error_details': [{'test': suite_name, 'message': str(e)}],
                'skip_details': []
            }
    
    def _print_suite_summary(self, suite_name, result):
        """Print summary for a test suite"""
        total_tests = result['tests_run']
        successes = result['successes']
        failures = result['failures']
        errors = result['errors']
        skipped = result['skipped']
        
        print(f"Results for {suite_name}:")
        print(f"  Total Tests: {total_tests}")
        print(f"  ✅ Passed: {successes}")
        print(f"  ❌ Failed: {failures}")
        print(f"  🚨 Errors: {errors}")
        print(f"  ⏭️  Skipped: {skipped}")
        
        if total_tests > 0:
            success_rate = (successes / total_tests) * 100
            print(f"  📊 Success Rate: {success_rate:.1f}%")
        
        # Print failure/error details if any
        if failures > 0:
            print("  Failures:")
            for failure in result['failure_details'][:3]:  # Show first 3
                print(f"    - {failure['test']}: {failure['message'][:100]}...")
        
        if errors > 0:
            print("  Errors:")
            for error in result['error_details'][:3]:  # Show first 3
                print(f"    - {error['test']}: {error['message'][:100]}...")
    
    def _generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        print("=" * 80)
        print("COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        total_duration = self.overall_end_time - self.overall_start_time
        
        # Overall statistics
        total_tests = sum(result['tests_run'] for result in self.results.values())
        total_successes = sum(result['successes'] for result in self.results.values())
        total_failures = sum(result['failures'] for result in self.results.values())
        total_errors = sum(result['errors'] for result in self.results.values())
        total_skipped = sum(result['skipped'] for result in self.results.values())
        
        print(f"Execution Time: {total_duration:.2f} seconds")
        print(f"Total Test Suites: {len(self.test_suites)}")
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {total_successes}")
        print(f"❌ Failed: {total_failures}")
        print(f"🚨 Errors: {total_errors}")
        print(f"⏭️  Skipped: {total_skipped}")
        
        if total_tests > 0:
            overall_success_rate = (total_successes / total_tests) * 100
            print(f"📊 Overall Success Rate: {overall_success_rate:.1f}%")
        
        print()
        
        # Detailed breakdown by test suite
        print("DETAILED BREAKDOWN BY TEST SUITE:")
        print("-" * 50)
        
        for suite_name, result in self.results.items():
            status = "✅ PASS" if result['failures'] == 0 and result['errors'] == 0 else "❌ FAIL"
            print(f"{suite_name}: {status}")
            print(f"  Tests: {result['tests_run']}, "
                  f"Passed: {result['successes']}, "
                  f"Failed: {result['failures']}, "
                  f"Errors: {result['errors']}")
        
        print()
        
        # Recommendations
        self._generate_recommendations()
        
        # Save detailed report to file
        self._save_report_to_file()
    
    def _generate_recommendations(self):
        """Generate recommendations based on test results"""
        print("RECOMMENDATIONS:")
        print("-" * 20)
        
        total_failures = sum(result['failures'] for result in self.results.values())
        total_errors = sum(result['errors'] for result in self.results.values())
        
        if total_failures == 0 and total_errors == 0:
            print("🎉 All tests passed! Your HL7 processing system is working correctly.")
            print("   Consider running these tests regularly as part of your CI/CD pipeline.")
        else:
            print("⚠️  Some tests failed. Please review the following:")
            
            if total_failures > 0:
                print(f"   - {total_failures} test failures indicate logic issues that need fixing")
            
            if total_errors > 0:
                print(f"   - {total_errors} test errors indicate setup or environment issues")
            
            print("   - Check the detailed error messages above")
            print("   - Ensure all dependencies are installed correctly")
            print("   - Verify that test data and directories are properly set up")
        
        # Specific recommendations for HL7 validation
        if 'HL7 Message Validation' in self.results:
            hl7_result = self.results['HL7 Message Validation']
            if hl7_result['skipped'] > 0:
                print("   - HL7 validation tests were skipped - ensure enhancedHl7 folder exists with test files")
    
    def _save_report_to_file(self):
        """Save detailed test report to JSON file"""
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'execution_time': self.overall_end_time - self.overall_start_time,
            'summary': {
                'total_suites': len(self.test_suites),
                'total_tests': sum(result['tests_run'] for result in self.results.values()),
                'total_successes': sum(result['successes'] for result in self.results.values()),
                'total_failures': sum(result['failures'] for result in self.results.values()),
                'total_errors': sum(result['errors'] for result in self.results.values()),
                'total_skipped': sum(result['skipped'] for result in self.results.values())
            },
            'detailed_results': self.results
        }
        
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            print(f"📄 Detailed report saved to: {report_file}")
        except Exception as e:
            print(f"⚠️  Could not save report to file: {e}")
    
    def _calculate_overall_success(self):
        """Calculate overall success status"""
        total_failures = sum(result['failures'] for result in self.results.values())
        total_errors = sum(result['errors'] for result in self.results.values())
        
        return total_failures == 0 and total_errors == 0


def main():
    """Main function to run all tests"""
    runner = ComprehensiveTestRunner(verbosity=2)
    success = runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
