# Test-specific requirements for HL7 Enhancement Python Tool
# These are additional packages needed for running the test suite

# Core testing framework (built-in with Python)
# unittest - included with Python standard library

# Mock and testing utilities
mock>=4.0.3

# Test coverage reporting
coverage>=6.0

# Test result formatting
pytest>=7.0.0
pytest-html>=3.1.0
pytest-cov>=4.0.0

# For XML test reports (useful in CI/CD)
pytest-xvfb>=2.0.0

# Memory profiling for performance tests
memory-profiler>=0.60.0

# Time-based testing utilities
freezegun>=1.2.0

# Temporary file and directory utilities (built-in)
# tempfile - included with Python standard library

# JSON handling (built-in)
# json - included with Python standard library

# Path handling (built-in)
# pathlib - included with Python standard library

# Regular expressions (built-in)
# re - included with Python standard library

# Date and time utilities (built-in)
# datetime - included with Python standard library

# Operating system interface (built-in)
# os - included with Python standard library

# System-specific parameters (built-in)
# sys - included with Python standard library

# String I/O (built-in)
# io - included with Python standard library

# File operations (built-in)
# shutil - included with Python standard library

# Note: The main project dependencies are in ../requirements.txt
# This file only includes additional testing-specific packages
