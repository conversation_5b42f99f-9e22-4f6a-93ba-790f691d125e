{"timestamp": "2025-07-03T07:44:50.577955", "execution_time": 67.72471356391907, "summary": {"total_suites": 6, "total_tests": 83, "total_successes": 37, "total_failures": 8, "total_errors": 38, "total_skipped": 0}, "detailed_results": {"HL7 Message Validation": {"tests_run": 6, "failures": 2, "errors": 0, "skipped": 0, "successes": 4, "failure_details": [{"test": "test_encoding_characters (tests.test_hl7_validation.TestHL7MessageValidation.test_encoding_characters)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_validation.py\", line 194, in test_encoding_characters\n    self.assertEqual(\nAssertionError: 1 != 0 : Files with incorrect encoding characters: [\n  {\n    \"file\": \"enhancedHl7\\\\ADT-A04\\\\ADT-A04_PATID1234-123456789-PSSN123121234-1234567-PATID567-_MSG00001_606d4303-a634-4b97-b21e-f5af8a19533b.hl7\",\n    \"expected\": \"^~\\\\&\",\n    \"actual\": \"^~&a\"\n  }\n]\n"}, {"test": "test_message_type_specific_segments (tests.test_hl7_validation.TestHL7MessageValidation.test_message_type_specific_segments)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_validation.py\", line 238, in test_message_type_specific_segments\n    self.assertEqual(\nAssertionError: 6 != 0 : Files missing required segments: [\n  {\n    \"file\": \"enhancedHl7\\\\ADT-A04\\\\ADT-A04_HC08529312_Q7486688090T13603923582_186e5567-1c89-4446-add5-4685778fc0c4.hl7\",\n    \"message_type\": \"ADT\",\n    \"missing_segments\": [\n      \"EVN\"\n    ]\n  },\n  {\n    \"file\": \"enhancedHl7\\\\ADT-A04\\\\ADT-A04_HC08529312_Q7486688090T13603923582_6d6a77fa-d5a4-43da-8cb0-7386a8640bcd.hl7\",\n    \"message_type\": \"ADT\",\n    \"missing_segments\": [\n      \"EVN\"\n    ]\n  },\n  {\n    \"file\": \"enhancedHl7\\\\ADT-A04\\\\ADT-A04_HC08807325_Q7486688308T13603923948_0c16a873-f3cc-4acb-9047-432b70c204c8.hl7\",\n    \"message_type\": \"ADT\",\n    \"missing_segments\": [\n      \"EVN\"\n    ]\n  },\n  {\n    \"file\": \"enhancedHl7\\\\ADT-A04\\\\ADT-A04_HC08807325_Q7486688308T13603923948_ba184666-3364-4544-a885-3073b4d3d11a.hl7\",\n    \"message_type\": \"ADT\",\n    \"missing_segments\": [\n      \"EVN\"\n    ]\n  },\n  {\n    \"file\": \"enhancedHl7\\\\ADT-A04\\\\ADT-A04_HC08807325_Q7486688308T13603923948_c00e2873-d8a7-4821-b5fa-15500ebf4821.hl7\",\n    \"message_type\": \"ADT\",\n    \"missing_segments\": [\n      \"EVN\"\n    ]\n  },\n  {\n    \"file\": \"enhancedHl7\\\\ADT-A04\\\\ADT-A04_HC08807325_Q7486688308T13603923948_d53c6ea3-dc58-41cd-9aa9-ea93b4f6c373.hl7\",\n    \"message_type\": \"ADT\",\n    \"missing_segments\": [\n      \"EVN\"\n    ]\n  }\n]\n"}], "error_details": [], "skip_details": []}, "HL7 Processor": {"tests_run": 18, "failures": 1, "errors": 3, "skipped": 0, "successes": 14, "failure_details": [{"test": "test_ensure_evn_segment_exists (tests.test_process_hl7.TestHL7Processor.test_ensure_evn_segment_exists)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_process_hl7.py\", line 162, in test_ensure_evn_segment_exists\n    evn_segment = message.segment('EVN')\n                  ^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\containers.py\", line 412, in segment\n    match = self.segments(segment_id)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\containers.py\", line 431, in segments\n    raise KeyError(\"No %s segments\" % segment_id)\nKeyError: 'No EVN segments'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_process_hl7.py\", line 165, in test_ensure_evn_segment_exists\n    self.fail(\"EVN segment was not added\")\nAssertionError: EVN segment was not added\n"}], "error_details": [{"test": "test_get_message_type_adt (tests.test_process_hl7.TestHL7Processor.test_get_message_type_adt)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_process_hl7.py\", line 270, in test_get_message_type_adt\n    msg_type = processor.get_message_type(message)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7Processor' object has no attribute 'get_message_type'\n"}, {"test": "test_get_message_type_unknown (tests.test_process_hl7.TestHL7Processor.test_get_message_type_unknown)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_process_hl7.py\", line 284, in test_get_message_type_unknown\n    msg_type = processor.get_message_type(message)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7Processor' object has no attribute 'get_message_type'\n"}, {"test": "test_load_config_file_not_found (tests.test_process_hl7.TestHL7Processor.test_load_config_file_not_found)", "message": "Traceback (most recent call last):\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1375, in patched\n    return func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_process_hl7.py\", line 196, in test_load_config_file_not_found\n    processor = HL7Processor(self.source_dir, self.output_dir)\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\processHl7.py\", line 51, in __init__\n    self.setup_logging()\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\processHl7.py\", line 62, in setup_logging\n    activity_handler = logging.FileHandler('processing_activity.log')\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\logging\\__init__.py\", line 1181, in __init__\n    StreamHandler.__init__(self, self._open())\n                                 ^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\logging\\__init__.py\", line 1213, in _open\n    return open_func(self.baseFilename, self.mode,\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1124, in __call__\n    return self._mock_call(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1128, in _mock_call\n    return self._execute_mock_call(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1183, in _execute_mock_call\n    raise effect\nFileNotFoundError\n"}], "skip_details": []}, "HL7 Processing Error": {"tests_run": 2, "failures": 0, "errors": 0, "skipped": 0, "successes": 2, "failure_details": [], "error_details": [], "skip_details": []}, "HL7 Validator": {"tests_run": 23, "failures": 0, "errors": 23, "skipped": 0, "successes": 0, "failure_details": [], "error_details": [{"test": "test_calculate_validation_statistics (tests.test_validator.TestHL7Validator.test_calculate_validation_statistics)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 298, in test_calculate_validation_statistics\n    stats = validator.calculate_validation_statistics(results)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'calculate_validation_statistics'\n"}, {"test": "test_find_hl7_files (tests.test_validator.TestHL7Validator.test_find_hl7_files)", "message": "Traceback (most recent call last):\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1375, in patched\n    return func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 234, in test_find_hl7_files\n    hl7_files = validator.find_hl7_files(self.test_folder)\n                ^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'find_hl7_files'\n"}, {"test": "test_find_hl7_files_empty_directory (tests.test_validator.TestHL7Validator.test_find_hl7_files_empty_directory)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 245, in test_find_hl7_files_empty_directory\n    hl7_files = validator.find_hl7_files(empty_dir)\n                ^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'find_hl7_files'\n"}, {"test": "test_format_validation_errors (tests.test_validator.TestHL7Validator.test_format_validation_errors)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 283, in test_format_validation_errors\n    formatted = validator.format_validation_errors(errors)\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'format_validation_errors'\n"}, {"test": "test_generate_validation_report (tests.test_validator.TestHL7Validator.test_generate_validation_report)", "message": "Traceback (most recent call last):\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1372, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Program Files\\Python311\\Lib\\contextlib.py\", line 137, in __enter__\n    return next(self.gen)\n           ^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1354, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\contextlib.py\", line 505, in enter_context\n    result = _enter(cm)\n             ^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1443, in __enter__\n    original, local = self.get_original()\n                      ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1416, in get_original\n    raise AttributeError(\nAttributeError: <module 'validator' from 'C:\\\\Users\\\\<USER>\\\\Repos\\\\Hl7EnhancePython\\\\validator.py'> does not have the attribute 'validate_folder'\n"}, {"test": "test_get_message_type_adt (tests.test_validator.TestHL7Validator.test_get_message_type_adt)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 167, in test_get_message_type_adt\n    msg_type = validator.get_message_type(message)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'get_message_type'\n"}, {"test": "test_get_message_type_complex (tests.test_validator.TestHL7Validator.test_get_message_type_complex)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 180, in test_get_message_type_complex\n    msg_type = validator.get_message_type(message)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'get_message_type'\n"}, {"test": "test_get_message_type_missing_msh (tests.test_validator.TestHL7Validator.test_get_message_type_missing_msh)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 187, in test_get_message_type_missing_msh\n    message = hl7.parse(invalid_content)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\parser.py\", line 98, in parse\n    plan = create_parse_plan(strmsg, factory)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\parser.py\", line 348, in create_parse_plan\n    raise ParseException(\nhl7.exceptions.ParseException: First segment is PID, must be one of MHS, FHS or BHS\n"}, {"test": "test_parse_hl7_file_empty (tests.test_validator.TestHL7Validator.test_parse_hl7_file_empty)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 82, in test_parse_hl7_file_empty\n    message, raw_content = validator.parse_hl7_file(test_file)\n                           ^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'parse_hl7_file'\n"}, {"test": "test_parse_hl7_file_invalid (tests.test_validator.TestHL7Validator.test_parse_hl7_file_invalid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 71, in test_parse_hl7_file_invalid\n    message, raw_content = validator.parse_hl7_file(test_file)\n                           ^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'parse_hl7_file'\n"}, {"test": "test_parse_hl7_file_valid (tests.test_validator.TestHL7Validator.test_parse_hl7_file_valid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 59, in test_parse_hl7_file_valid\n    message, raw_content = validator.parse_hl7_file(test_file)\n                           ^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'parse_hl7_file'\n"}, {"test": "test_validate_folder (tests.test_validator.TestHL7Validator.test_validate_folder)", "message": "Traceback (most recent call last):\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1372, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Program Files\\Python311\\Lib\\contextlib.py\", line 137, in __enter__\n    return next(self.gen)\n           ^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1354, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\contextlib.py\", line 505, in enter_context\n    result = _enter(cm)\n             ^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1443, in __enter__\n    original, local = self.get_original()\n                      ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1416, in get_original\n    raise AttributeError(\nAttributeError: <module 'validator' from 'C:\\\\Users\\\\<USER>\\\\Repos\\\\Hl7EnhancePython\\\\validator.py'> does not have the attribute 'find_hl7_files'\n"}, {"test": "test_validate_hl7_version_invalid (tests.test_validator.TestHL7Validator.test_validate_hl7_version_invalid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 100, in test_validate_hl7_version_invalid\n    is_valid, error_msg = validator.validate_hl7_version(message, '2.8')\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_hl7_version'\n"}, {"test": "test_validate_hl7_version_missing_msh (tests.test_validator.TestHL7Validator.test_validate_hl7_version_missing_msh)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 109, in test_validate_hl7_version_missing_msh\n    message = hl7.parse(invalid_content)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\parser.py\", line 98, in parse\n    plan = create_parse_plan(strmsg, factory)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\parser.py\", line 348, in create_parse_plan\n    raise ParseException(\nhl7.exceptions.ParseException: First segment is PID, must be one of MHS, FHS or BHS\n"}, {"test": "test_validate_hl7_version_valid (tests.test_validator.TestHL7Validator.test_validate_hl7_version_valid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 91, in test_validate_hl7_version_valid\n    is_valid, error_msg = validator.validate_hl7_version(message, '2.8')\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_hl7_version'\n"}, {"test": "test_validate_msh_segment_missing_fields (tests.test_validator.TestHL7Validator.test_validate_msh_segment_missing_fields)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 131, in test_validate_msh_segment_missing_fields\n    is_valid, errors = validator.validate_msh_segment(message)\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_msh_segment'\n"}, {"test": "test_validate_msh_segment_valid (tests.test_validator.TestHL7Validator.test_validate_msh_segment_valid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 120, in test_validate_msh_segment_valid\n    is_valid, errors = validator.validate_msh_segment(message)\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_msh_segment'\n"}, {"test": "test_validate_required_segments_missing (tests.test_validator.TestHL7Validator.test_validate_required_segments_missing)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 149, in test_validate_required_segments_missing\n    is_valid, missing_segments = validator.validate_required_segments(message, 'ADT')\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_required_segments'\n"}, {"test": "test_validate_required_segments_unknown_type (tests.test_validator.TestHL7Validator.test_validate_required_segments_unknown_type)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 158, in test_validate_required_segments_unknown_type\n    is_valid, missing_segments = validator.validate_required_segments(message, 'UNKNOWN')\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_required_segments'\n"}, {"test": "test_validate_required_segments_valid (tests.test_validator.TestHL7Validator.test_validate_required_segments_valid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 140, in test_validate_required_segments_valid\n    is_valid, missing_segments = validator.validate_required_segments(message, 'ADT')\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_required_segments'\n"}, {"test": "test_validate_single_file_invalid (tests.test_validator.TestHL7Validator.test_validate_single_file_invalid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 211, in test_validate_single_file_invalid\n    result = validator.validate_single_file(test_file, '2.8')\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_single_file'\n"}, {"test": "test_validate_single_file_parse_error (tests.test_validator.TestHL7Validator.test_validate_single_file_parse_error)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 222, in test_validate_single_file_parse_error\n    result = validator.validate_single_file(test_file, '2.8')\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_single_file'\n"}, {"test": "test_validate_single_file_valid (tests.test_validator.TestHL7Validator.test_validate_single_file_valid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_validator.py\", line 199, in test_validate_single_file_valid\n    result = validator.validate_single_file(test_file, '2.8')\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: module 'validator' has no attribute 'validate_single_file'\n"}], "skip_details": []}, "HL7 to Markdown Converter": {"tests_run": 14, "failures": 1, "errors": 1, "skipped": 0, "successes": 12, "failure_details": [{"test": "test_handle_file_encoding (tests.test_hl7tomd.TestHL7ToMarkdownConverter.test_handle_file_encoding)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7tomd.py\", line 200, in test_handle_file_encoding\n    self.assertEqual(content, self.sample_hl7_1)\nAssertionError: 'MSH|[84 chars]|2.8\\nEVN|A04|202411101127|||123456\\nPID|1||12[178 chars]7890' != 'MSH|[84 chars]|2.8\\rEVN|A04|202411101127|||123456\\rPID|1||12[178 chars]7890'\nDiff is 1086 characters long. Set self.maxDiff to None to see it.\n"}], "error_details": [{"test": "test_convert_hl7_to_markdown_batch_basic (tests.test_hl7tomd.TestHL7ToMarkdownConverter.test_convert_hl7_to_markdown_batch_basic)", "message": "Traceback (most recent call last):\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1372, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Program Files\\Python311\\Lib\\contextlib.py\", line 137, in __enter__\n    return next(self.gen)\n           ^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1354, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\contextlib.py\", line 505, in enter_context\n    result = _enter(cm)\n             ^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1443, in __enter__\n    original, local = self.get_original()\n                      ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1416, in get_original\n    raise AttributeError(\nAttributeError: <module 'hl7tomd' from 'C:\\\\Users\\\\<USER>\\\\Repos\\\\Hl7EnhancePython\\\\hl7tomd.py'> does not have the attribute 'OUTPUT_FOLDER_PATH'\n"}], "skip_details": []}, "HL7 Compliance Fixer": {"tests_run": 20, "failures": 4, "errors": 11, "skipped": 0, "successes": 5, "failure_details": [{"test": "test_create_backup_folder (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_create_backup_folder)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 70, in test_create_backup_folder\n    self.assertTrue(fixer.backup_folder.startswith('backup_before_fix_'))\nAssertionError: False is not true\n"}, {"test": "test_fix_all_messages (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_fix_all_messages)", "message": "Traceback (most recent call last):\n  File \"C:\\Program Files\\Python311\\Lib\\unittest\\mock.py\", line 1375, in patched\n    return func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 284, in test_fix_all_messages\n    self.assertEqual(fixer.fixed_count, 1)  # needs_fix.hl7\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: 3 != 1\n"}, {"test": "test_fix_all_messages_with_errors (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_fix_all_messages_with_errors)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 305, in test_fix_all_messages_with_errors\n    self.assertEqual(fixer.fixed_count, 1)\nAssertionError: 2 != 1\n"}, {"test": "test_logging_setup (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_logging_setup)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 313, in test_logging_setup\n    self.assertEqual(fixer.logger.name, 'HL7ComplianceFixer')\nAssertionError: 'hl7_compliance_fixer' != 'HL7ComplianceFixer'\n- hl7_compliance_fixer\n? ^^ ^^         ^^\n+ HL7ComplianceFixer\n? ^^ ^         ^\n\n"}], "error_details": [{"test": "test_add_evn_segment_already_exists (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_add_evn_segment_already_exists)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 160, in test_add_evn_segment_already_exists\n    modified_content, was_modified = fixer.add_evn_segment(self.hl7_with_evn)\n                                     ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'add_evn_segment'\n"}, {"test": "test_add_evn_segment_needed (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_add_evn_segment_needed)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 146, in test_add_evn_segment_needed\n    modified_content, was_modified = fixer.add_evn_segment(self.hl7_without_evn)\n                                     ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'add_evn_segment'\n"}, {"test": "test_add_evn_segment_non_adt_message (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_add_evn_segment_non_adt_message)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 169, in test_add_evn_segment_non_adt_message\n    modified_content, was_modified = fixer.add_evn_segment(self.non_adt_message)\n                                     ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'add_evn_segment'\n"}, {"test": "test_backup_file (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_backup_file)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 182, in test_backup_file\n    fixer.backup_file(test_file)\n    ^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'backup_file'\n"}, {"test": "test_find_msh_line_and_message_type_invalid_msh (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_find_msh_line_and_message_type_invalid_msh)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 100, in test_find_msh_line_and_message_type_invalid_msh\n    msh_line, message_type, msh_index = fixer.find_msh_line_and_message_type(lines)\n                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'find_msh_line_and_message_type'\n"}, {"test": "test_find_msh_line_and_message_type_no_msh (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_find_msh_line_and_message_type_no_msh)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 89, in test_find_msh_line_and_message_type_no_msh\n    msh_line, message_type, msh_index = fixer.find_msh_line_and_message_type(lines)\n                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'find_msh_line_and_message_type'\n"}, {"test": "test_find_msh_line_and_message_type_valid (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_find_msh_line_and_message_type_valid)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 77, in test_find_msh_line_and_message_type_valid\n    msh_line, message_type, msh_index = fixer.find_msh_line_and_message_type(lines)\n                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'find_msh_line_and_message_type'\n"}, {"test": "test_fix_single_file_already_compliant (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_fix_single_file_already_compliant)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 219, in test_fix_single_file_already_compliant\n    result = fixer.fix_single_file(test_file)\n             ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'fix_single_file'\n"}, {"test": "test_fix_single_file_invalid_content (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_fix_single_file_invalid_content)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 255, in test_fix_single_file_invalid_content\n    result = fixer.fix_single_file(test_file)\n             ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'fix_single_file'\n"}, {"test": "test_fix_single_file_needs_fixing (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_fix_single_file_needs_fixing)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 201, in test_fix_single_file_needs_fixing\n    result = fixer.fix_single_file(test_file)\n             ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'fix_single_file'\n"}, {"test": "test_fix_single_file_non_adt (tests.test_hl7_compliance_fixer.TestHL7ComplianceFixer.test_fix_single_file_non_adt)", "message": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Repos\\Hl7EnhancePython\\tests\\test_hl7_compliance_fixer.py\", line 237, in test_fix_single_file_non_adt\n    result = fixer.fix_single_file(test_file)\n             ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'HL7ComplianceFixer' object has no attribute 'fix_single_file'\n"}], "skip_details": []}}}