#!/usr/bin/env python3
"""
Quick fix for the remaining problematic files
"""

import os

def fix_file(file_path):
    """Fix a specific file"""
    print(f"Fixing {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"  Error reading: {e}")
        return False
    
    lines = content.strip().split('\n')
    if not lines:
        return False
    
    fixed = False
    
    # Fix MSH segment and add EVN if needed
    for i, line in enumerate(lines):
        if line.startswith('MSH|'):
            # Fix encoding characters issue where sending app is set to ^~\&
            if '|^~\\&|^~\\&|' in line:
                line = line.replace('|^~\\&|^~\\&|', '|^~\\&|Millennium|')
                lines[i] = line
                fixed = True
                print(f"  Fixed MSH sending application")
            
            # Check if EVN segment exists
            has_evn = any(l.strip().startswith('EVN|') for l in lines if l.strip())
            if not has_evn:
                # Extract timestamp from MSH-7
                fields = line.split('|')
                timestamp = fields[7] if len(fields) > 7 and fields[7] else "20240902082341"
                
                # Create EVN segment
                evn_segment = f"EVN|A04|{timestamp}||||"
                lines.insert(i + 1, evn_segment)
                print(f"  Added EVN segment")
                fixed = True
            break
    
    if fixed:
        try:
            fixed_content = '\n'.join(lines) + '\n'
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"  Successfully fixed {file_path}")
            return True
        except Exception as e:
            print(f"  Error writing: {e}")
            return False
    else:
        print(f"  No fixes needed")
        return False

def main():
    """Main function"""
    files_to_fix = [
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_ba184666-3364-4544-a885-3073b4d3d11a.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_c00e2873-d8a7-4821-b5fa-15500ebf4821.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_d53c6ea3-dc58-41cd-9aa9-ea93b4f6c373.hl7"
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            fix_file(file_path)
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
