#!/usr/bin/env python3
"""
Comprehensive HL7 file fix script to address encoding characters and missing EVN segments
"""

import os
import shutil
import glob
import re

def fix_msh_encoding_characters(msh_line):
    """Fix encoding characters in MSH segment"""
    # MSH segment structure: MSH|^~\&|SendingApp|...
    # The encoding characters should be exactly ^~\&
    
    if not msh_line.startswith('MSH|'):
        return msh_line, False
    
    # Find the position after MSH|
    if len(msh_line) < 8:  # MSH| + at least 4 encoding chars
        return msh_line, False
    
    # Extract current encoding characters (positions 4-7 after MSH|)
    current_encoding = msh_line[4:8]
    correct_encoding = '^~\\&'
    
    if current_encoding != correct_encoding:
        # Replace the encoding characters
        fixed_line = 'MSH|' + correct_encoding + msh_line[8:]
        return fixed_line, True
    
    return msh_line, False

def fix_file(file_path):
    """Fix a specific HL7 file"""
    print(f"Fixing {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"  Error reading file: {e}")
        return False
    
    lines = content.strip().split('\n')
    if not lines:
        print(f"  File is empty")
        return False
    
    fixed = False
    
    # Fix MSH segment encoding characters
    for i, line in enumerate(lines):
        if line.startswith('MSH|'):
            fixed_line, was_fixed = fix_msh_encoding_characters(line)
            if was_fixed:
                lines[i] = fixed_line
                print(f"  Fixed encoding characters in MSH segment")
                fixed = True
            break
    
    # Check if EVN segment exists for ADT messages
    has_evn = any(line.strip().startswith('EVN|') for line in lines if line.strip())
    
    if not has_evn:
        # Find MSH segment and check if it's an ADT message
        for i, line in enumerate(lines):
            if line.startswith('MSH|'):
                # Parse MSH segment to get message type
                fields = line.split('|')
                if len(fields) > 9:
                    message_type_field = fields[9]  # MSH.9
                    if message_type_field.startswith('ADT^'):
                        # Extract timestamp from MSH.7
                        timestamp = fields[7] if len(fields) > 7 and fields[7] else "20240902082341"
                        
                        # Extract event type from message type (e.g., ADT^A04 -> A04)
                        event_type = "A04"  # Default
                        if '^' in message_type_field:
                            parts = message_type_field.split('^')
                            if len(parts) > 1:
                                event_type = parts[1]
                        
                        # Create EVN segment
                        evn_segment = f"EVN|{event_type}|{timestamp}||||"
                        lines.insert(i + 1, evn_segment)
                        print(f"  Added missing EVN segment: {evn_segment}")
                        fixed = True
                break
    
    # Write fixed content only if changes were made
    if fixed:
        try:
            # Create backup
            backup_path = file_path + ".backup"
            shutil.copy2(file_path, backup_path)
            
            fixed_content = '\n'.join(lines) + '\n'
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"  Successfully fixed {file_path}")
            return True
        except Exception as e:
            print(f"  Error writing file: {e}")
            return False
    else:
        print(f"  No fixes needed for {file_path}")
        return False

def main():
    """Main function"""
    print("HL7 Comprehensive Fix Script")
    print("=" * 50)
    
    # Fix the specific files mentioned in test failures first
    specific_files = [
        "enhancedHl7/ADT-A04/ADT-A04_PATID1234-123456789-PSSN123121234-1234567-PATID567-_MSG00001_606d4303-a634-4b97-b21e-f5af8a19533b.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08529312_Q7486688090T13603923582_186e5567-1c89-4446-add5-4685778fc0c4.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08529312_Q7486688090T13603923582_6d6a77fa-d5a4-43da-8cb0-7386a8640bcd.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_0c16a873-f3cc-4acb-9047-432b70c204c8.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_ba184666-3364-4544-a885-3073b4d3d11a.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_c00e2873-d8a7-4821-b5fa-15500ebf4821.hl7",
        "enhancedHl7/ADT-A04/ADT-A04_HC08807325_Q7486688308T13603923948_d53c6ea3-dc58-41cd-9aa9-ea93b4f6c373.hl7"
    ]
    
    print("Fixing specific files mentioned in test failures...")
    fixed_count = 0
    for file_path in specific_files:
        if os.path.exists(file_path):
            if fix_file(file_path):
                fixed_count += 1
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nFixed {fixed_count} specific files.")
    
    # Ask user if they want to scan and fix all files
    response = input("\nDo you want to scan and fix all HL7 files? (y/n): ").lower().strip()
    if response == 'y':
        print("\nScanning all HL7 files...")
        
        # Scan all HL7 files
        hl7_patterns = [
            "enhancedHl7/**/*.hl7",
            "enhancedHl7/*.hl7"
        ]
        
        all_files = []
        for pattern in hl7_patterns:
            all_files.extend(glob.glob(pattern, recursive=True))
        
        # Remove duplicates and already processed files
        all_files = list(set(all_files))
        all_files = [f for f in all_files if f not in specific_files]
        
        print(f"Found {len(all_files)} additional files to check...")
        
        files_needing_fix = []
        for file_path in all_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.strip().split('\n')
                needs_fix = False
                
                # Check for encoding character issues
                for line in lines:
                    if line.startswith('MSH|'):
                        if len(line) >= 8:
                            current_encoding = line[4:8]
                            if current_encoding != '^~\\&':
                                needs_fix = True
                                break
                
                # Check for missing EVN in ADT messages
                if not needs_fix:
                    has_evn = any(line.strip().startswith('EVN|') for line in lines if line.strip())
                    if not has_evn:
                        for line in lines:
                            if line.startswith('MSH|'):
                                fields = line.split('|')
                                if len(fields) > 9 and fields[9].startswith('ADT^'):
                                    needs_fix = True
                                    break
                
                if needs_fix:
                    files_needing_fix.append(file_path)
                    
            except Exception as e:
                print(f"Error checking {file_path}: {e}")
        
        if files_needing_fix:
            print(f"\nFound {len(files_needing_fix)} files that need fixes.")
            print("Fixing all files...")
            
            fixed_count = 0
            for file_path in files_needing_fix:
                try:
                    if fix_file(file_path):
                        fixed_count += 1
                    if fixed_count % 50 == 0 and fixed_count > 0:
                        print(f"  Progress: {fixed_count}/{len(files_needing_fix)} files fixed")
                except Exception as e:
                    print(f"  Error fixing {file_path}: {e}")
            
            print(f"\nCompleted fixing {fixed_count} additional files.")
        else:
            print("\nNo additional files found that need fixes.")
    
    print("\nFix script completed!")

if __name__ == "__main__":
    main()
