"""
Unit tests for hl7_compliance_fixer.py - HL7 compliance fixing tool
"""

import unittest
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open

# Import the module under test
import sys
sys.path.append('.')
from hl7_compliance_fixer import HL7ComplianceFixer


class TestHL7ComplianceFixer(unittest.TestCase):
    """Test suite for HL7ComplianceFixer class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_folder = os.path.join(self.temp_dir, 'test_hl7')
        os.makedirs(self.test_folder)
        
        # Sample HL7 content without EVN segment (needs fixing)
        self.hl7_without_evn = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\n"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F|||123 Main St^^Hometown^CA^12345^USA||(123)456-7890|||S|C|123456789\n"
            "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr.|||||||||||1234567890"
        )
        
        # Sample HL7 content with EVN segment (already compliant)
        self.hl7_with_evn = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ADT^A04|123456|P|2.8\n"
            "EVN|A04|202411101127|||123456\n"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F|||123 Main St^^Hometown^CA^12345^USA||(123)456-7890|||S|C|123456789\n"
            "PV1|1|I|ICU^01^01||||12345^Smith^John^A^^Dr.|||||||||||1234567890"
        )
        
        # Non-ADT message (should not be modified)
        self.non_adt_message = (
            "MSH|^~\\&|SendingApp|SendingFac|ReceivingApp|ReceivingFac|202411101127||ORU^R01|123456|P|2.8\n"
            "PID|1||123456^^^Hospital^MR||Doe^Jane^A^^Ms.||19800101|F\n"
            "OBR|1|123456|789012|CBC^Complete Blood Count||202411101127"
        )
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def test_fixer_initialization(self):
        """Test HL7ComplianceFixer initialization"""
        fixer = HL7ComplianceFixer(self.test_folder)
        
        self.assertEqual(fixer.folder_path, self.test_folder)
        self.assertEqual(fixer.fixed_count, 0)
        self.assertEqual(fixer.already_compliant_count, 0)
        self.assertEqual(fixer.error_count, 0)
        self.assertIsNotNone(fixer.backup_folder)
        self.assertIsNotNone(fixer.logger)
    
    def test_create_backup_folder(self):
        """Test backup folder creation"""
        fixer = HL7ComplianceFixer(self.test_folder)
        
        # Backup folder should be created during initialization
        self.assertTrue(os.path.exists(fixer.backup_folder))
        self.assertTrue(fixer.backup_folder.startswith('backup_before_fix_'))
    
    def test_find_msh_line_and_message_type_valid(self):
        """Test finding MSH line and message type in valid HL7 content"""
        fixer = HL7ComplianceFixer(self.test_folder)
        lines = self.hl7_without_evn.split('\n')
        
        msh_line, message_type, msh_index = fixer.find_msh_line_and_message_type(lines)
        
        self.assertIsNotNone(msh_line)
        self.assertEqual(message_type, 'ADT^A04')
        self.assertEqual(msh_index, 0)
        self.assertIn('MSH|', msh_line)
    
    def test_find_msh_line_and_message_type_no_msh(self):
        """Test finding MSH line when MSH segment is missing"""
        fixer = HL7ComplianceFixer(self.test_folder)
        lines = ["PID|1||123456", "PV1|1|I|ICU"]
        
        msh_line, message_type, msh_index = fixer.find_msh_line_and_message_type(lines)
        
        self.assertIsNone(msh_line)
        self.assertIsNone(message_type)
        self.assertEqual(msh_index, -1)
    
    def test_find_msh_line_and_message_type_invalid_msh(self):
        """Test finding MSH line with invalid MSH segment"""
        fixer = HL7ComplianceFixer(self.test_folder)
        lines = ["MSH|^~\\&|SendingApp", "PID|1||123456"]  # Incomplete MSH
        
        msh_line, message_type, msh_index = fixer.find_msh_line_and_message_type(lines)
        
        self.assertIsNotNone(msh_line)
        self.assertIsNone(message_type)  # Message type extraction should fail
        self.assertEqual(msh_index, 0)
    
    def test_create_evn_segment_with_timestamp(self):
        """Test creating EVN segment with provided timestamp"""
        fixer = HL7ComplianceFixer(self.test_folder)
        message_type = 'ADT^A04'
        timestamp = '202411101127'
        
        evn_segment = fixer.create_evn_segment(message_type, timestamp)
        
        self.assertIn('EVN|A04|202411101127', evn_segment)
    
    def test_create_evn_segment_without_timestamp(self):
        """Test creating EVN segment without provided timestamp"""
        fixer = HL7ComplianceFixer(self.test_folder)
        message_type = 'ADT^A08'
        
        evn_segment = fixer.create_evn_segment(message_type, None)
        
        self.assertIn('EVN|A08|', evn_segment)
        # Should contain a generated timestamp
        self.assertTrue(len(evn_segment.split('|')[2]) >= 14)  # YYYYMMDDHHMMSS format
    
    def test_create_evn_segment_different_event_types(self):
        """Test creating EVN segment for different ADT event types"""
        fixer = HL7ComplianceFixer(self.test_folder)
        
        test_cases = [
            ('ADT^A01', 'A01'),
            ('ADT^A04', 'A04'),
            ('ADT^A08', 'A08'),
            ('ADT^A11', 'A11')
        ]
        
        for message_type, expected_event in test_cases:
            evn_segment = fixer.create_evn_segment(message_type, '202411101127')
            self.assertIn(f'EVN|{expected_event}|', evn_segment)
    
    def test_add_evn_segment_needed(self):
        """Test adding EVN segment when needed"""
        fixer = HL7ComplianceFixer(self.test_folder)
        
        modified_content, was_modified = fixer.add_evn_segment(self.hl7_without_evn)
        
        self.assertTrue(was_modified)
        self.assertIn('EVN|A04|', modified_content)
        # EVN should be after MSH
        lines = modified_content.split('\n')
        msh_index = next(i for i, line in enumerate(lines) if line.startswith('MSH'))
        evn_index = next(i for i, line in enumerate(lines) if line.startswith('EVN'))
        self.assertEqual(evn_index, msh_index + 1)
    
    def test_add_evn_segment_already_exists(self):
        """Test adding EVN segment when it already exists"""
        fixer = HL7ComplianceFixer(self.test_folder)
        
        modified_content, was_modified = fixer.add_evn_segment(self.hl7_with_evn)
        
        self.assertFalse(was_modified)
        self.assertEqual(modified_content, self.hl7_with_evn)
    
    def test_add_evn_segment_non_adt_message(self):
        """Test adding EVN segment to non-ADT message (should not modify)"""
        fixer = HL7ComplianceFixer(self.test_folder)
        
        modified_content, was_modified = fixer.add_evn_segment(self.non_adt_message)
        
        self.assertFalse(was_modified)
        self.assertEqual(modified_content, self.non_adt_message)
    
    def test_backup_file(self):
        """Test backing up a file"""
        # Create test file
        test_file = os.path.join(self.test_folder, 'test.hl7')
        with open(test_file, 'w') as f:
            f.write(self.hl7_without_evn)
        
        fixer = HL7ComplianceFixer(self.test_folder)
        fixer.backup_file(test_file)
        
        # Check backup file exists
        backup_file = os.path.join(fixer.backup_folder, 'test.hl7')
        self.assertTrue(os.path.exists(backup_file))
        
        # Check backup content matches original
        with open(backup_file, 'r') as f:
            backup_content = f.read()
        self.assertEqual(backup_content, self.hl7_without_evn)
    
    def test_fix_single_file_needs_fixing(self):
        """Test fixing a single file that needs EVN segment"""
        # Create test file
        test_file = os.path.join(self.test_folder, 'needs_fix.hl7')
        with open(test_file, 'w') as f:
            f.write(self.hl7_without_evn)
        
        fixer = HL7ComplianceFixer(self.test_folder)
        result = fixer.fix_single_file(test_file)
        
        self.assertTrue(result)
        self.assertEqual(fixer.fixed_count, 1)
        
        # Check file was modified
        with open(test_file, 'r') as f:
            modified_content = f.read()
        self.assertIn('EVN|A04|', modified_content)
    
    def test_fix_single_file_already_compliant(self):
        """Test fixing a file that's already compliant"""
        # Create test file
        test_file = os.path.join(self.test_folder, 'compliant.hl7')
        with open(test_file, 'w') as f:
            f.write(self.hl7_with_evn)
        
        fixer = HL7ComplianceFixer(self.test_folder)
        result = fixer.fix_single_file(test_file)
        
        self.assertTrue(result)
        self.assertEqual(fixer.already_compliant_count, 1)
        
        # Check file was not modified
        with open(test_file, 'r') as f:
            content = f.read()
        self.assertEqual(content, self.hl7_with_evn)
    
    def test_fix_single_file_non_adt(self):
        """Test fixing a non-ADT file"""
        # Create test file
        test_file = os.path.join(self.test_folder, 'non_adt.hl7')
        with open(test_file, 'w') as f:
            f.write(self.non_adt_message)
        
        fixer = HL7ComplianceFixer(self.test_folder)
        result = fixer.fix_single_file(test_file)
        
        self.assertTrue(result)
        self.assertEqual(fixer.already_compliant_count, 1)
        
        # Check file was not modified
        with open(test_file, 'r') as f:
            content = f.read()
        self.assertEqual(content, self.non_adt_message)
    
    def test_fix_single_file_invalid_content(self):
        """Test fixing a file with invalid content"""
        # Create test file with invalid content
        test_file = os.path.join(self.test_folder, 'invalid.hl7')
        with open(test_file, 'w') as f:
            f.write('Invalid HL7 content')
        
        fixer = HL7ComplianceFixer(self.test_folder)
        result = fixer.fix_single_file(test_file)
        
        self.assertFalse(result)
        self.assertEqual(fixer.error_count, 1)
    
    @patch('os.walk')
    def test_fix_all_messages(self, mock_walk):
        """Test fixing all messages in folder"""
        # Create test files
        test_files = [
            ('needs_fix.hl7', self.hl7_without_evn),
            ('compliant.hl7', self.hl7_with_evn),
            ('non_adt.hl7', self.non_adt_message)
        ]
        
        for filename, content in test_files:
            file_path = os.path.join(self.test_folder, filename)
            with open(file_path, 'w') as f:
                f.write(content)
        
        # Mock os.walk to return our test files
        mock_walk.return_value = [
            (self.test_folder, [], [f[0] for f in test_files])
        ]
        
        fixer = HL7ComplianceFixer(self.test_folder)
        fixer.fix_all_messages()
        
        # Check statistics
        self.assertEqual(fixer.fixed_count, 1)  # needs_fix.hl7
        self.assertEqual(fixer.already_compliant_count, 2)  # compliant.hl7 and non_adt.hl7
        self.assertEqual(fixer.error_count, 0)
    
    def test_fix_all_messages_with_errors(self):
        """Test fixing all messages when some files have errors"""
        # Create test files including invalid one
        test_files = [
            ('valid.hl7', self.hl7_without_evn),
            ('invalid.hl7', 'Invalid content')
        ]
        
        for filename, content in test_files:
            file_path = os.path.join(self.test_folder, filename)
            with open(file_path, 'w') as f:
                f.write(content)
        
        fixer = HL7ComplianceFixer(self.test_folder)
        fixer.fix_all_messages()
        
        # Check that valid file was processed and invalid file caused error
        self.assertEqual(fixer.fixed_count, 1)
        self.assertEqual(fixer.error_count, 1)
    
    def test_logging_setup(self):
        """Test that logging is properly set up"""
        fixer = HL7ComplianceFixer(self.test_folder)
        
        self.assertIsNotNone(fixer.logger)
        self.assertEqual(fixer.logger.name, 'HL7ComplianceFixer')
    
    @patch('hl7_compliance_fixer.HL7ComplianceFixer.fix_all_messages')
    def test_main_function(self, mock_fix_all):
        """Test main function execution"""
        with patch('hl7_compliance_fixer.HL7ComplianceFixer') as mock_fixer_class:
            mock_fixer = MagicMock()
            mock_fixer_class.return_value = mock_fixer
            
            # Import and run main
            from hl7_compliance_fixer import main
            main()
            
            # Verify fixer was created and fix_all_messages was called
            mock_fixer_class.assert_called_once_with("enhancedHl7")
            mock_fixer.fix_all_messages.assert_called_once()


if __name__ == '__main__':
    unittest.main()
